<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="POSSystem.Views.Layouts.SalesViewGrid"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:POSSystem.Views.Layouts"
             xmlns:views="clr-namespace:POSSystem.Views"
             xmlns:vm="clr-namespace:POSSystem.ViewModels"
             xmlns:converters="clr-namespace:POSSystem.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:POSSystem.Controls"
             xmlns:behaviors="clr-namespace:POSSystem.Behaviors"
             FocusManager.IsFocusScope="True"
             Focusable="True"
             IsTabStop="True"
             TabIndex="0"
             mc:Ignorable="d"
             d:DesignHeight="720" d:DesignWidth="1280"
             Background="{DynamicResource MaterialDesignBackground}">

    <UserControl.Resources>
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <converters:LengthToVisibilityConverter x:Key="LengthToVisibilityConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToStockColorConverter x:Key="BooleanToStockColorConverter"/>
        <converters:TotalStockConverter x:Key="TotalStockConverter"/>
        <converters:Base64ToImageConverter x:Key="Base64ToImageConverter"/>
        <converters:NullableBooleanConverter x:Key="NullableBooleanConverter"/>
        <converters:MultiBooleanConverter x:Key="MultiBooleanConverter"/>
        <converters:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
        <converters:QuantityDisplayConverter x:Key="QuantityDisplayConverter"/>
        <converters:QuantityWithUnitConverter x:Key="QuantityWithUnitConverter"/>
        <converters:StockDisplayConverter x:Key="StockDisplayConverter"/>

        <!-- Add additional converters -->
        <BooleanToVisibilityConverter x:Key="StandardBooleanToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:MultiBooleanToVisibilityConverter x:Key="MultiBooleanToVisibilityConverter"/>

        <!-- Additional color resources that will dynamically change with theme -->
        <Color x:Key="SuccessColor">#43A047</Color>
        <!-- Green -->
        <Color x:Key="WarningColor">#FB8C00</Color>
        <!-- Orange/Amber -->

        <!-- System Color Brushes (will inherit from App-level resources) -->
        <SolidColorBrush x:Key="SuccessBrush" Color="{DynamicResource SuccessColor}"/>
        <SolidColorBrush x:Key="WarningBrush" Color="{DynamicResource WarningColor}"/>

        <!-- Use default theme brushes directly -->
        <!-- Remove the custom color definitions and reference the app's theme brushes directly in the XAML -->
        <converters:CurrencyFormatConverter x:Key="CurrencyFormatConverter"/>
    </UserControl.Resources>

    <!-- Keyboard Shortcuts -->
    <UserControl.InputBindings>
        <!-- Search Shortcuts -->
        <KeyBinding Key="F3" Command="{Binding SearchCommand}"/>
        <KeyBinding Modifiers="Control" Key="F" Command="{Binding SearchCommand}"/>

        <!-- Payment Shortcuts -->
        <KeyBinding Key="F4" Command="{Binding ProcessPaymentCommand}"/>
        <KeyBinding Modifiers="Control" Key="Enter" Command="{Binding ProcessPaymentCommand}"/>

        <!-- Cart Management -->
        <KeyBinding Key="Delete" Command="{Binding RemoveFromCartCommand}" CommandParameter="{Binding SelectedCartItem.Product.Id}"/>
        <KeyBinding Key="OemPlus" Command="{Binding IncreaseQuantityCommand}"/>
        <KeyBinding Key="OemMinus" Command="{Binding DecreaseQuantityCommand}"/>
        <KeyBinding Key="Add" Command="{Binding IncreaseQuantityCommand}"/>
        <KeyBinding Key="Subtract" Command="{Binding DecreaseQuantityCommand}"/>

        <!-- Customer Shortcuts -->
        <KeyBinding Key="F5" Command="{Binding RedeemPointsCommand}"/>

        <!-- Misc Shortcuts -->
        <KeyBinding Key="Escape" Command="{Binding ClearSearchCommand}"/>
        <KeyBinding Modifiers="Control" Key="N" Command="{Binding CreateNewCartCommand}"/>
    </UserControl.InputBindings>

    <!-- ✅ PERFORMANCE FIX: Optimized DialogHost for better rendering performance -->
    <materialDesign:DialogHost Identifier="SalesDialog"
                              CloseOnClickAway="True"
                              OverlayBackground="{DynamicResource MaterialDesignSelection}"
                              DialogMargin="32"
                              DialogTheme="Inherit"
                              DialogOpened="SalesDialog_DialogOpened"
                              DialogClosing="SalesDialog_DialogClosing">
        <Grid x:Name="RootContentGrid" Margin="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header with title and info - ✅ PERFORMANCE FIX: Removed DropShadowEffect for better rendering performance -->
            <Border Grid.Row="0" materialDesign:ElevationAssist.Elevation="Dp2"
                Background="{DynamicResource PrimaryHueDarkBrush}">
                <Grid>
                    <Grid Margin="12,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="0.8*"/>
                            <ColumnDefinition Width="0.8*"/>
                            <ColumnDefinition Width="1.4*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Left Section: Product Card Preview -->
                        <materialDesign:Card Grid.Column="0" Background="{DynamicResource PrimaryHueDarkBrush}" UniformCornerRadius="10" Margin="0,0,6,0"
                                       materialDesign:ElevationAssist.Elevation="Dp2">
                            <materialDesign:Card.Style>
                                <Style TargetType="materialDesign:Card" BasedOn="{StaticResource {x:Type materialDesign:Card}}">
                                    <Setter Property="Visibility" Value="Visible"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding HasSelectedCartItem}" Value="True">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </materialDesign:Card.Style>
                            <Grid Margin="8">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="CartArrowDown" Width="28" Height="28"
                                                       Foreground="#B3E5FC" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{DynamicResource SelectProduct}" Foreground="White" />

                                </StackPanel>
                            </Grid>
                        </materialDesign:Card>

                        <!-- Product Preview Card -->
                        <materialDesign:Card Grid.Column="0" Background="{DynamicResource PrimaryHueDarkBrush}" UniformCornerRadius="10" Margin="0,0,6,0"
                                       materialDesign:ElevationAssist.Elevation="Dp2">
                            <materialDesign:Card.Style>
                                <Style TargetType="materialDesign:Card" BasedOn="{StaticResource {x:Type materialDesign:Card}}">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding HasSelectedCartItem}" Value="True">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </materialDesign:Card.Style>
                            <Grid Margin="10,6">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Product Header with Image and Basic Info -->
                                <Grid Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Product Image -->
                                    <Border Grid.Column="0" Background="{DynamicResource MaterialDesignPaper}" CornerRadius="8" Width="42" Height="42" Margin="0,0,10,0">
                                        <Grid>
                                            <materialDesign:PackIcon Kind="ShoppingOutline" Width="26" Height="26"
                                                             Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                             HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <materialDesign:PackIcon.Style>
                                                    <Style TargetType="materialDesign:PackIcon">
                                                        <Setter Property="Visibility" Value="Visible" />
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding SelectedCartItem.Product.ImageData}" Value="{x:Null}">
                                                                <Setter Property="Visibility" Value="Visible" />
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding SelectedCartItem.Product.ImageData}" Value="">
                                                                <Setter Property="Visibility" Value="Visible" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </materialDesign:PackIcon.Style>
                                            </materialDesign:PackIcon>
                                            <Image behaviors:AsyncImage.Base64Source="{Binding SelectedCartItem.Product.ImageData}"
                                             Stretch="Uniform" Width="36" Height="36">
                                                <Image.Style>
                                                    <Style TargetType="Image">
                                                        <Setter Property="Visibility" Value="Collapsed" />
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding SelectedCartItem.Product.ImageData}" Value="{x:Null}">
                                                                <Setter Property="Visibility" Value="Collapsed" />
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding SelectedCartItem.Product.ImageData}" Value="">
                                                                <Setter Property="Visibility" Value="Collapsed" />
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding SelectedCartItem.Product.ImageData, Converter={StaticResource NullToBooleanConverter}}" Value="True">
                                                                <Setter Property="Visibility" Value="Visible" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Image.Style>
                                            </Image>
                                        </Grid>
                                    </Border>

                                    <!-- Product Info -->
                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                        <TextBlock Text="{Binding SelectedCartItem.Product.Name}" Foreground="{DynamicResource SystemBackgroundBrush}"
                                             FontWeight="SemiBold" FontSize="14" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis"/>
                                        <WrapPanel Orientation="Horizontal" Margin="0,2,0,0">
                                            <StackPanel Orientation="Horizontal" Margin="0,0,8,0">
                                                <materialDesign:PackIcon Kind="Barcode" Foreground="{DynamicResource PrimaryHueLightForegroundBrush}" Width="12" Height="12" VerticalAlignment="Center" Margin="0,0,2,0"/>
                                                <TextBlock Text="{Binding SelectedCartItem.Product.Barcode, TargetNullValue='No Barcode'}"
                                                     Foreground="{DynamicResource PrimaryHueLightForegroundBrush}" FontSize="11"/>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FolderOutline" Foreground="{DynamicResource PrimaryHueLightForegroundBrush}" Width="12" Height="12" VerticalAlignment="Center" Margin="0,0,2,0"/>
                                                <TextBlock Text="{Binding SelectedCartItem.Product.Category.Name, TargetNullValue='Uncategorized'}"
                                                     Foreground="{DynamicResource PrimaryHueLightForegroundBrush}" FontSize="11"/>
                                            </StackPanel>
                                        </WrapPanel>
                                    </StackPanel>
                                </Grid>

                                <!-- Stock Status -->
                                <Border Grid.Row="1" Margin="0,6,0,0" CornerRadius="4" Height="20">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Setter Property="Background" Value="{DynamicResource SuccessBrush}"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SelectedCartItem.Product.IsOutOfStock}" Value="True">
                                                    <Setter Property="Background" Value="{DynamicResource SystemAlertBrush}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SelectedCartItem.Product.IsLowStock}" Value="True">
                                                    <Setter Property="Background" Value="{DynamicResource WarningBrush}"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Column="0" Margin="6,0,2,0" Width="12" Height="12" VerticalAlignment="Center">
                                            <materialDesign:PackIcon.Style>
                                                <Style TargetType="materialDesign:PackIcon">
                                                    <Setter Property="Foreground" Value="White"/>
                                                    <Setter Property="Kind" Value="CheckCircle"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding SelectedCartItem.Product.IsOutOfStock}" Value="True">
                                                            <Setter Property="Kind" Value="AlertCircle"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding SelectedCartItem.Product.IsLowStock}" Value="True">
                                                            <Setter Property="Kind" Value="AlertCircleOutline"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </materialDesign:PackIcon.Style>
                                        </materialDesign:PackIcon>

                                        <TextBlock Grid.Column="1" Foreground="White" VerticalAlignment="Center" FontSize="11" Margin="0,0,6,0">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Text" Value="{DynamicResource InStock}"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding SelectedCartItem.Product.IsOutOfStock}" Value="True">
                                                            <Setter Property="Text" Value="{DynamicResource OutOfStock}"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding SelectedCartItem.Product.IsLowStock}" Value="True">
                                                            <Setter Property="Text" Value="{DynamicResource LowStock}"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>

                                        <TextBlock Grid.Column="2"
                                             Text="{Binding SelectedCartItem.Product, Converter={StaticResource StockDisplayConverter}}"
                                             Foreground="White"
                                             FontWeight="Medium"
                                             FontSize="11"
                                             VerticalAlignment="Center"
                                             Margin="0,0,6,0"/>
                                    </Grid>
                                </Border>

                                <!-- Price and Stock Info -->
                                <Grid Grid.Row="2" Margin="0,6,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Unit Price -->
                                    <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="0,0,12,0">
                                        <TextBlock Text="{DynamicResource Unit}" Foreground="#82B1FF" FontSize="11" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding SelectedCartItem.UnitPrice, Converter={StaticResource CurrencyFormatConverter}}"
                                             Foreground="White" FontWeight="Medium" FontSize="12" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- Quantity -->
                                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                                        <TextBlock Text="{DynamicResource Qty}" Foreground="#82B1FF" FontSize="11" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding SelectedCartItem, Converter={StaticResource QuantityDisplayConverter}}"
                                             Foreground="White" FontWeight="Medium" FontSize="12" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- Total -->
                                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                                        <TextBlock Text="Total: " Foreground="#82B1FF" FontSize="11" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding SelectedCartItem.Total, Converter={StaticResource CurrencyFormatConverter}}"
                                             Foreground="#E1F5FE" FontWeight="SemiBold" FontSize="12" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- Middle Section: Customer Info -->
                        <Grid Grid.Column="1" Margin="6,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Search by Loyalty (Top Row) -->
                            <Grid Grid.Row="0" VerticalAlignment="Center" Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Add Customer Button -->
                                <Button Grid.Column="0"
                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                                Background="{DynamicResource SystemBackgroundBrush}"
                                Foreground="{DynamicResource PrimaryHueMidBrush}"
                                Width="32"
                                Height="32"
                                Click="LookupCustomer_Click"
                                ToolTip="{DynamicResource SelectCustomer}"
                                Margin="0,0,6,0">
                                    <materialDesign:PackIcon Kind="AccountPlus" Width="16" Height="16"/>
                                </Button>

                                <!-- Remove Customer Button -->
                                <Button Grid.Column="1"
                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                                Background="{DynamicResource SystemBackgroundBrush}"
                                Foreground="{DynamicResource SystemAlertBrush}"
                                Width="32"
                                Height="32"
                                Click="ClearCustomer_Click"
                                ToolTip="Clear Customer"
                                Visibility="{Binding SelectedCustomer, Converter={StaticResource NullToVisibilityConverter}}"
                                Margin="0,0,6,0">
                                    <materialDesign:PackIcon Kind="AccountRemove" Width="16" Height="16"/>
                                </Button>

                                <!-- Loyalty Code Input -->
                                <Border Grid.Column="2"
                                Background="{DynamicResource SystemBackgroundBrush}"
                                CornerRadius="16"
                                Effect="{StaticResource MaterialDesignShadowDepth2}"
                                Margin="0,0,6,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <materialDesign:PackIcon Kind="CardAccountDetails"
                                        Grid.Column="0"
                                        Width="16"
                                        Height="16"
                                        Foreground="{DynamicResource SystemForegroundMediumBrush}"
                                        VerticalAlignment="Center"
                                        Margin="10,0,0,0"/>
                                        <TextBox Grid.Column="1"
                                        x:Name="txtLoyaltyCode"
                                        Style="{StaticResource MaterialDesignTextBox}"
                                        materialDesign:HintAssist.Hint="{DynamicResource loyaltySearch}"
                                        materialDesign:TextFieldAssist.HasClearButton="True"
                                        materialDesign:TextFieldAssist.DecorationVisibility="Hidden"
                                        BorderThickness="0"
                                        Foreground="{DynamicResource SystemForegroundBrush}"
                                        Background="Transparent"
                                        CaretBrush="{DynamicResource PrimaryHueMidBrush}"
                                        FontSize="13"
                                        VerticalAlignment="Center"
                                        KeyDown="TxtLoyaltyCode_KeyDown"
                                        Margin="6,0,6,0"
                                        Height="32"/>
                                    </Grid>
                                </Border>

                                <!-- Search Button -->
                                <Button Grid.Column="3"
                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                                Background="#2196F3"
                                Foreground="White"
                                Width="32"
                                Height="32"
                                Click="SearchLoyaltyCode_Click"
                                ToolTip="Search Customer by Loyalty Code">
                                    <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16"/>
                                </Button>
                            </Grid>

                            <!-- Customer Info (Bottom Row) -->
                            <Border Grid.Row="1"
                            Background="{DynamicResource PrimaryHueMidBrush}"
                            CornerRadius="10"
                            Padding="10,8">
                                <!-- Removed shadow to reduce per-frame composition cost -->

                                <!-- Main Container -->
                                <Grid>
                                    <!-- Empty State -->
                                    <Grid>
                                        <Grid.Style>
                                            <Style TargetType="Grid">
                                                <Setter Property="Visibility" Value="Visible"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding HasLoyaltyCustomer}" Value="True">
                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Grid.Style>
                                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                            <materialDesign:PackIcon Kind="AccountPlusOutline"
                                                               Width="28" Height="28"
                                                               Foreground="#B3E5FC"
                                                               HorizontalAlignment="Center"/>
                                            <TextBlock Text="{DynamicResource NoCustomerSelected}"
                                                 Foreground="White"
                                                 FontSize="14"
                                                 TextAlignment="Center"
                                                 Margin="0,6,0,2"/>
                                            <TextBlock Text="{DynamicResource NoCustomerSelectedHint}"
                                                 Foreground="#B3E5FC"
                                                 FontSize="11"
                                                 TextAlignment="Center"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- Customer Info (When Selected) -->
                                    <Grid>
                                        <Grid.Style>
                                            <Style TargetType="Grid">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding HasLoyaltyCustomer}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Grid.Style>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Customer Name and Points -->
                                        <StackPanel Grid.Column="0" Orientation="Vertical" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding SelectedCustomer.FullName}"
                                            Foreground="White"
                                            FontSize="16"
                                            FontWeight="Medium"
                                            VerticalAlignment="Center"/>

                                            <!-- Loyalty Points (Inline) -->
                                            <StackPanel Orientation="Horizontal"
                                                  Visibility="{Binding HasLoyaltyCustomer, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                <materialDesign:PackIcon Kind="Gift"
                                                                  Width="14"
                                                                  Height="14"
                                                                  Foreground="#E1F5FE"
                                                                  VerticalAlignment="Center"
                                                                  Margin="0,0,3,0"/>

                                                <TextBlock Text="{Binding CustomerLoyaltyPoints, StringFormat='{}{0:N0} points'}"
                                                Foreground="#E1F5FE"
                                                FontSize="12"
                                                VerticalAlignment="Center"/>

                                                <TextBlock Text=" | "
                                                Foreground="#E1F5FE"
                                                Margin="3,0"
                                                FontSize="12"
                                                VerticalAlignment="Center"/>

                                                <TextBlock Text="{Binding LoyaltyPointsEarned, StringFormat='+{0:N0} new'}"
                                                Foreground="#BBDEFB"
                                                FontWeight="SemiBold"
                                                FontSize="12"
                                                VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- Redeem Button -->
                                        <Button Grid.Column="1"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Height="28"
                                        BorderBrush="#E1F5FE"
                                        Background="#1976D2"
                                        BorderThickness="1"
                                        Foreground="White"
                                        Click="RedeemPoints_Click"
                                        IsEnabled="{Binding CanRedeemPoints}"
                                        Visibility="{Binding HasLoyaltyCustomer, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        Margin="6,0,0,0"
                                        Padding="8,0"
                                        materialDesign:ButtonAssist.CornerRadius="14"
                                        materialDesign:ShadowAssist.ShadowDepth="Depth1">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="GiftOutline"
                                                                  Width="14"
                                                                  Height="14"
                                                                  VerticalAlignment="Center"
                                                                  Margin="0,0,4,0"/>
                                                <TextBlock Text="Redeem" FontSize="11" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Border>
                        </Grid>

                        <!-- Right Section: Totals Display -->
                        <materialDesign:Card Grid.Column="2" Background="{DynamicResource PrimaryHueDarkBrush}" UniformCornerRadius="12" Padding="16,2" materialDesign:ElevationAssist.Elevation="Dp3" Margin="6,0,0,0">
                            <!-- Removed per-frame DropShadowEffect to reduce GPU/CPU cost -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Left Side: Amounts -->
                                <Grid Grid.Column="0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Subtotal Label -->
                                    <TextBlock Grid.Column="0" Grid.Row="0"
                                        Text="{DynamicResource Subtotal}"
                                        FontSize="14"
                                        Foreground="#B3E5FC"
                                        Margin="0,2,6,0"
                                        VerticalAlignment="Top"
                                        HorizontalAlignment="Left"/>

                                    <!-- Discount Label - Only visible when there's a discount -->
                                    <TextBlock Grid.Column="1" Grid.Row="0"
                                        Text="{DynamicResource Discount}"
                                        FontSize="12"
                                        Foreground="#FF8A80"
                                        Margin="6,2,0,0"
                                        VerticalAlignment="Top"
                                        HorizontalAlignment="Left">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource {x:Type TextBlock}}">
                                                <Setter Property="Visibility" Value="Collapsed" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding CurrentCart.DiscountAmount, Converter={StaticResource NullableBooleanConverter}}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>

                                    <!-- Subtotal Value -->
                                    <TextBlock Grid.Column="0" Grid.Row="1"
                                        Text="{Binding CurrentCart.Subtotal, Converter={StaticResource CurrencyFormatConverter}}"
                                        FontSize="16"
                                        Foreground="#E1F5FE"
                                        Margin="0,0,6,6"
                                        VerticalAlignment="Top"
                                        HorizontalAlignment="Left"
                                        FontWeight="Medium"/>

                                    <!-- Discount Value -->
                                    <TextBlock Grid.Column="1" Grid.Row="1"
                                        Text="{Binding CurrentCart.DiscountAmount, Converter={StaticResource CurrencyFormatConverter}}"
                                        FontSize="16"
                                        Foreground="#FF8A80"
                                        Margin="6,0,0,6"
                                        VerticalAlignment="Top"
                                        HorizontalAlignment="Left"
                                        FontWeight="Medium">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource {x:Type TextBlock}}">
                                                <Setter Property="Visibility" Value="Collapsed" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding CurrentCart.DiscountAmount, Converter={StaticResource NullableBooleanConverter}}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>

                                    <!-- Bulk Pricing Row - Only visible when there are bulk savings -->
                                    <TextBlock Grid.Column="0" Grid.Row="2"
                                        Text="Bulk Savings"
                                        FontSize="12"
                                        Foreground="#4CAF50"
                                        Margin="0,2,6,0"
                                        VerticalAlignment="Top"
                                        HorizontalAlignment="Left">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource {x:Type TextBlock}}">
                                                <Setter Property="Visibility" Value="Collapsed" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding CurrentCart.HasBulkPricing}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>

                                    <TextBlock Grid.Column="1" Grid.Row="2"
                                        Text="{Binding CurrentCart.TotalBulkSavings, Converter={StaticResource CurrencyFormatConverter}}"
                                        FontSize="14"
                                        Foreground="#4CAF50"
                                        Margin="6,0,0,6"
                                        VerticalAlignment="Top"
                                        HorizontalAlignment="Left"
                                        FontWeight="Medium">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock" BasedOn="{StaticResource {x:Type TextBlock}}">
                                                <Setter Property="Visibility" Value="Collapsed" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding CurrentCart.HasBulkPricing}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>

                                    <!-- Total Row - Spans Both Columns -->
                                    <Grid Grid.Column="0" Grid.Row="3" Grid.ColumnSpan="2">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0"
                                           Text="{DynamicResource Total}"
                                           FontSize="16"
                                           Foreground="White"
                                           FontWeight="SemiBold"
                                           Margin="0,2,8,2"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"/>

                                        <!-- Remove Viewbox to prevent bitmap scaling blur; size text directly -->
                                        <Grid Grid.Column="1" Height="60" VerticalAlignment="Center" UseLayoutRounding="True" SnapsToDevicePixels="True">
                                            <TextBlock
                                               Text="{Binding CurrentCart.GrandTotal, Converter={StaticResource CurrencyFormatConverter}}"
                                               FontFamily="DSEG7 Modern, DSEG7 Classic, Digital-7, 7 Segment, LED Digital, Consolas, Monospace"
                                               FontSize="56"
                                               FontWeight="Heavy"
                                               Foreground="#00FF00"
                                               TextAlignment="Center"
                                               VerticalAlignment="Center"
                                               HorizontalAlignment="Center"
                                               TextOptions.TextFormattingMode="Display"
                                               TextOptions.TextRenderingMode="ClearType"
                                               TextOptions.TextHintingMode="Fixed"
                                               RenderOptions.ClearTypeHint="Enabled"
                                               UseLayoutRounding="True"
                                               SnapsToDevicePixels="True"/>
                                        </Grid>
                                    </Grid>
                                </Grid>

                                <!-- Right Side: Payment Button -->
                                <Button Grid.Column="1"
                                  Style="{StaticResource MaterialDesignRaisedButton}"
                                  Height="80"
                                  Width="90"
                                  Background="#4CAF50"
                                  Foreground="White"
                                  materialDesign:ButtonAssist.CornerRadius="18"
                                  materialDesign:ShadowAssist.ShadowDepth="Depth2"
                                  Click="ProcessPayment_Click"
                                  Margin="12,0,0,0"
                                  VerticalAlignment="Center">

                                    <Button.IsEnabled>
                                        <MultiBinding Converter="{StaticResource MultiBooleanConverter}">
                                            <Binding Path="CurrentCart" Converter="{StaticResource NullToBooleanConverter}" />
                                            <Binding Path="CurrentCart.HasItems" />
                                        </MultiBinding>
                                    </Button.IsEnabled>

                                    <StackPanel Orientation="Vertical">
                                        <materialDesign:PackIcon Kind="CreditCardOutline" Width="28" Height="28" Margin="0,0,0,6"
                                                         HorizontalAlignment="Center"/>
                                        <TextBlock Text="{DynamicResource Payment}" FontSize="14" FontWeight="Medium" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </Grid>
                        </materialDesign:Card>
                    </Grid>
                </Grid>
            </Border>

            <!-- Main Content Area -->
            <Grid Grid.Row="1" Background="{DynamicResource MaterialDesignBackground}" Margin="16,16,16,0">
                <!-- Removed page-level shadow to reduce composition cost -->
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="0.22*" MinWidth="180" MaxWidth="230"/>
                    <ColumnDefinition Width="0.65*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="0.55*" MinWidth="300"/>
                </Grid.ColumnDefinitions>

                <!-- Left Side - Categories Column -->
                <materialDesign:Card Grid.Column="0"
                               Background="{DynamicResource MaterialDesignPaper}"
                               Margin="0,8,8,8"
                               UniformCornerRadius="12"
                               materialDesign:ElevationAssist.Elevation="Dp3">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Categories Header -->
                        <Border Grid.Row="0"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Padding="16,12">
                            <TextBlock Text="{DynamicResource ProductFilters}"
                                 Foreground="{DynamicResource MaterialDesignPaper}"
                                 FontSize="16"
                                 FontWeight="Medium"/>
                        </Border>

                        <!-- Categories List with All Categories Button at Top -->
                        <Grid Grid.Row="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- All Categories Button -->
                            <Button Grid.Row="0"
                             Style="{StaticResource MaterialDesignFlatButton}"
                             HorizontalContentAlignment="Left"
                             HorizontalAlignment="Stretch"
                             Margin="8,8,8,0"
                             Click="AllCategories_Click">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Kind="ViewGridOutline"
                                                     Grid.Column="0"
                                                     Width="24"
                                                     Height="24"
                                                     VerticalAlignment="Center"
                                                     Margin="0,0,12,0"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                                    <TextBlock Grid.Column="1"
                                         Text="{DynamicResource AllCategories}"
                                         VerticalAlignment="Center"
                                         FontSize="14"
                                         FontWeight="Medium"/>
                                </Grid>
                            </Button>

                            <!-- Favorites Filter Button -->
                            <Button Grid.Row="1"
                             Style="{StaticResource MaterialDesignFlatButton}"
                             HorizontalContentAlignment="Left"
                             HorizontalAlignment="Stretch"
                             Margin="8,0,8,0"
                             Click="Favorites_Click">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Kind="Heart"
                                                     Grid.Column="0"
                                                     Width="24"
                                                     Height="24"
                                                     VerticalAlignment="Center"
                                                     Margin="0,0,12,0"
                                                     Foreground="{DynamicResource SystemAlertBrush}"/>

                                    <TextBlock Grid.Column="1"
                                         Text="{DynamicResource Favorites}"
                                         VerticalAlignment="Center"
                                         FontSize="14"/>
                                </Grid>
                            </Button>

                            <!-- Popular Products Button -->
                            <Button Grid.Row="2"
                             Style="{StaticResource MaterialDesignFlatButton}"
                             HorizontalContentAlignment="Left"
                             HorizontalAlignment="Stretch"
                             Margin="8,0,8,8"
                             Click="PopularItems_Click">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Kind="TrendingUp"
                                                     Grid.Column="0"
                                                     Width="24"
                                                     Height="24"
                                                     VerticalAlignment="Center"
                                                     Margin="0,0,12,0"
                                                     Foreground="{DynamicResource PrimaryHueDarkBrush}"/>

                                    <TextBlock Grid.Column="1"
                                         Text="{DynamicResource Popular}"
                                         VerticalAlignment="Center"
                                         FontSize="14"/>
                                </Grid>
                            </Button>

                            <!-- Categories Separator Line -->
                            <Separator Grid.Row="3"
                                  Margin="12,0,12,8"
                                  Background="{DynamicResource MaterialDesignDivider}"/>

                            <!-- Regular Categories -->
                            <ListView Grid.Row="4"
                                ItemsSource="{Binding Categories}"
                                SelectedItem="{Binding SelectedCategory, Mode=TwoWay}"
                                BorderThickness="0"
                                Background="Transparent"
                                Padding="4"
                                x:Name="categoriesList"
                                SelectionChanged="CategoriesList_SelectionChanged">
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Kind="FolderOutline"
                                                             Grid.Column="0"
                                                             Width="24"
                                                             Height="24"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,12,0"
                                                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                                            <TextBlock Grid.Column="1"
                                                 Text="{Binding Name}"
                                                 VerticalAlignment="Center"
                                                 FontSize="14"/>
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem" BasedOn="{StaticResource MaterialDesignListBoxItem}">
                                        <Setter Property="Padding" Value="8,10"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="{DynamicResource PrimaryHueLightBrush}"/>
                                                <Setter Property="Foreground" Value="{DynamicResource PrimaryHueDarkBrush}"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </ListView.ItemContainerStyle>
                            </ListView>
                        </Grid>
                    </Grid>
                </materialDesign:Card>

                <!-- Product Area -->
                <Grid Grid.Column="1" Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Enhanced Search Box -->
                    <Grid Grid.Row="0" Margin="0,4,0,18">
                        <materialDesign:Card UniformCornerRadius="15"
                                      materialDesign:ElevationAssist.Elevation="Dp3"
                                      Background="{DynamicResource MaterialDesignPaper}"
                                      Margin="10,0"
                                      Padding="0">
                            <TextBox x:Name="txtSearch"
                              Style="{StaticResource MaterialDesignTextBox}"
                           materialDesign:HintAssist.Hint="{DynamicResource ProductSearchHint}"
                              materialDesign:HintAssist.Foreground="{DynamicResource PrimaryHueMidBrush}"
                           materialDesign:TextFieldAssist.HasClearButton="True"
                              materialDesign:TextFieldAssist.UnderlineBrush="Transparent"
                              materialDesign:TextFieldAssist.DecorationVisibility="Hidden"
                              BorderThickness="0"
                           Foreground="{DynamicResource MaterialDesignBody}"
                              Background="Transparent"
                              FontSize="16"
                              Padding="20,14"
                              Margin="36,0,0,0"
                              KeyDown="TxtSearch_KeyDown"
                              TextChanged="TxtSearch_TextChanged">
                                <TextBox.InputBindings>
                                    <KeyBinding Key="Escape" Command="{Binding ClearSearchCommand}"/>
                                </TextBox.InputBindings>
                                <TextBox.Resources>
                                    <Style TargetType="Border">
                                        <Setter Property="CornerRadius" Value="28"/>
                                    </Style>
                                    <DynamicResourceExtension x:Key="PrimaryBrush" ResourceKey="PrimaryHueMidBrush"/>
                                    <DynamicResourceExtension x:Key="SecondaryBrush" ResourceKey="SecondaryHueMidBrush"/>
                                </TextBox.Resources>
                                <!-- Removed search TextBox DropShadowEffect to reduce per-frame cost -->
                                <TextBox.Triggers>
                                    <EventTrigger RoutedEvent="GotFocus">
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetProperty="(materialDesign:HintAssist.Foreground)">
                                                    <DiscreteObjectKeyFrame KeyTime="0:0:0">
                                                        <DiscreteObjectKeyFrame.Value>
                                                            <DynamicResourceExtension ResourceKey="SecondaryHueMidBrush"/>
                                                        </DiscreteObjectKeyFrame.Value>
                                                    </DiscreteObjectKeyFrame>
                                                </ObjectAnimationUsingKeyFrames>
                                                <!-- Removed SearchBox shadow animation -->
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                    <EventTrigger RoutedEvent="LostFocus">
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetProperty="(materialDesign:HintAssist.Foreground)">
                                                    <DiscreteObjectKeyFrame KeyTime="0:0:0">
                                                        <DiscreteObjectKeyFrame.Value>
                                                            <DynamicResourceExtension ResourceKey="PrimaryHueMidBrush"/>
                                                        </DiscreteObjectKeyFrame.Value>
                                                    </DiscreteObjectKeyFrame>
                                                </ObjectAnimationUsingKeyFrames>
                                                <!-- Removed SearchBox shadow animation -->
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                </TextBox.Triggers>
                            </TextBox>
                            <!-- Removed search card shadow for perf -->
                        </materialDesign:Card>
                    </Grid>

                    <!-- Products Grid with Viewbox for better scaling -->
                    <Grid Grid.Row="2">
                        <!-- Diagnostic text to show product count -->
                        <TextBlock Text="{Binding FilteredProducts.Count, StringFormat='Products: {0}'}"
                               HorizontalAlignment="Right"
                               VerticalAlignment="Top"
                               Margin="0,0,10,5"
                               FontWeight="Bold"
                               Foreground="Red"
                               FontSize="16"
                               Panel.ZIndex="10" />


                            <!-- ✅ PERFORMANCE FIX: Use ListView with VirtualizingWrapPanel for better performance -->
                            <ListView ItemsSource="{Binding FilteredProducts}"
                                     x:Name="productsListView"
                                     ScrollViewer.CanContentScroll="True"
                                     ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                     ScrollViewer.VerticalScrollBarVisibility="Auto"
                                     VirtualizingPanel.IsVirtualizing="True"
                                     VirtualizingPanel.VirtualizationMode="Recycling"
                                     VirtualizingPanel.CacheLength="10"
                                     VirtualizingPanel.CacheLengthUnit="Item"
                                     SelectionMode="Single"
                                     BorderThickness="0"
                                     Background="Transparent"
                                     SelectionChanged="ProductGrid_SelectionChanged">
                                <ListView.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <!-- ✅ PERFORMANCE: VirtualizingWrapPanel for efficient rendering -->
                                        <controls:VirtualizingWrapPanel ItemWidth="142" ItemHeight="200" />
                                    </ItemsPanelTemplate>
                                </ListView.ItemsPanel>
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <!-- ✅ MODERN DESIGN: Enhanced product card with improved styling and accessibility -->
                                        <Border Cursor="Hand"
                                            Width="130"
                                            Height="188"
                                            Margin="6,6,6,12"
                                            Style="{StaticResource ModernProductCardStyle}"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Top"
                                            MouseDown="ProductCard_Click"
                                            Focusable="True"
                                            KeyDown="ProductCard_KeyDown"
                                            AutomationProperties.Name="{Binding Name}"
                                            AutomationProperties.HelpText="{Binding SellingPrice, StringFormat='Price: {0:N2} DA'}"
                                            ToolTip="{Binding Name}">
                                            <!-- ✅ PERFORMANCE: Smooth transitions with RenderTransform -->
                                            <Border.RenderTransform>
                                                <ScaleTransform/>
                                            </Border.RenderTransform>
                                            <Border.RenderTransformOrigin>
                                                <Point X="0.5" Y="0.5"/>
                                            </Border.RenderTransformOrigin>
                                            <Border.Triggers>
                                                <EventTrigger RoutedEvent="MouseEnter">
                                                    <BeginStoryboard Storyboard="{StaticResource ProductCardHoverEnterAnimation}"/>
                                                </EventTrigger>
                                                <EventTrigger RoutedEvent="MouseLeave">
                                                    <BeginStoryboard Storyboard="{StaticResource ProductCardHoverExitAnimation}"/>
                                                </EventTrigger>
                                            </Border.Triggers>
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="*"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- ✅ MODERN DESIGN: Enhanced status indicator with subtle pulse animation -->
                                                <Border Panel.ZIndex="2">
                                                    <Border.Style>
                                                        <Style TargetType="Border" BasedOn="{StaticResource ProductStatusIndicatorStyle}">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsOutOfStock}" Value="True">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                    <Setter Property="Background">
                                                                        <Setter.Value>
                                                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                                <GradientStop Color="#EF4444" Offset="0.0"/>
                                                                                <GradientStop Color="#DC2626" Offset="1.0"/>
                                                                            </LinearGradientBrush>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                    <!-- Removed forever animation to reduce per-frame costs -->
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding IsLowStock}" Value="True">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                    <Setter Property="Background">
                                                                        <Setter.Value>
                                                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                                <GradientStop Color="#F59E0B" Offset="0.0"/>
                                                                                <GradientStop Color="#D97706" Offset="1.0"/>
                                                                            </LinearGradientBrush>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                    <!-- Removed forever animation to reduce per-frame costs -->
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                    <materialDesign:PackIcon Kind="AlertCircle"
                                                                           Width="14" Height="14"
                                                                           Foreground="White"
                                                                           HorizontalAlignment="Center"
                                                                           VerticalAlignment="Center"/>
                                                </Border>

                                                <!-- ✅ MODERN DESIGN: Enhanced favorite toggle button -->
                                                <ToggleButton Style="{StaticResource ProductFavoriteButtonStyle}"
                                                              HorizontalAlignment="Left"
                                                              VerticalAlignment="Top"
                                                              Margin="8,8,0,0"
                                                              Command="{Binding DataContext.ToggleFavoriteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                              CommandParameter="{Binding}"
                                                              IsChecked="{Binding IsFavorited, Mode=OneWay}"
                                                              Panel.ZIndex="2"
                                                              ToolTip="Add to favorites">
                                                    <ToggleButton.Content>
                                                        <materialDesign:PackIcon Kind="HeartOutline"
                                                                                 Width="14" Height="14"
                                                                                 Foreground="#9CA3AF"/>
                                                    </ToggleButton.Content>
                                                    <materialDesign:ToggleButtonAssist.OnContent>
                                                        <materialDesign:PackIcon Kind="Heart"
                                                                                 Width="14" Height="14"
                                                                                 Foreground="#EF4444"/>
                                                    </materialDesign:ToggleButtonAssist.OnContent>
                                                </ToggleButton>

                                                <!-- ✅ MODERN DESIGN: Enhanced product image area -->
                                                <Border Grid.Row="0"
                                                        Style="{StaticResource ProductImageContainerStyle}"
                                                        Margin="0">
                                                    <Grid>
                                                        <!-- ✅ MODERN DESIGN: Enhanced fallback icon with better styling -->
                                                        <Border CornerRadius="50"
                                                                Width="60" Height="60"
                                                                Background="White"
                                                                HorizontalAlignment="Center"
                                                                VerticalAlignment="Center">
                                                            <Border.Style>
                                                                <Style TargetType="Border">
                                                                    <Setter Property="Visibility" Value="Collapsed" />
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding ImageData}" Value="{x:Null}">
                                                                            <Setter Property="Visibility" Value="Visible" />
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding ImageData}" Value="">
                                                                            <Setter Property="Visibility" Value="Visible" />
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>
                                                            <!-- Removed product image container shadow for perf -->
                                                            <materialDesign:PackIcon Kind="ShoppingOutline"
                                                                                     Width="32" Height="32"
                                                                                     HorizontalAlignment="Center"
                                                                                     VerticalAlignment="Center"
                                                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                                        </Border>

                                                        <!-- ✅ MODERN DESIGN: Enhanced product image with better styling -->
                                                        <Border CornerRadius="8"
                                                                Background="White"
                                                                BorderThickness="0"
                                                                Margin="12"
                                                                HorizontalAlignment="Center"
                                                                VerticalAlignment="Center">
                                                            <Border.Style>
                                                                <Style TargetType="Border">
                                                                    <Setter Property="Visibility" Value="Visible" />
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding ImageData}" Value="{x:Null}">
                                                                            <Setter Property="Visibility" Value="Collapsed" />
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding ImageData}" Value="">
                                                                            <Setter Property="Visibility" Value="Collapsed" />
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>
                                                            <!-- Removed image container shadow for perf -->
                                                            <!-- ✅ PERFORMANCE OPTIMIZED: Faster image rendering -->
                                                            <Image behaviors:AsyncImage.Base64Source="{Binding ImageData}"
                                                                   Stretch="Uniform"
                                                                   Width="75"
                                                                   Height="75"
                                                                   UseLayoutRounding="True"
                                                                   SnapsToDevicePixels="True"/>
                                                        </Border>
                                                    </Grid>
                                                </Border>

                                                <!-- ✅ MODERN DESIGN: Enhanced product info panel -->
                                                <Border Grid.Row="1"
                                                        Style="{StaticResource ProductInfoPanelStyle}">
                                                    <!-- ✅ MODERN DESIGN: Enhanced product name with better typography -->
                                                    <TextBlock Text="{Binding Name}"
                                                               Style="{StaticResource ProductNameTextStyle}"
                                                               VerticalAlignment="Center"
                                                               HorizontalAlignment="Center"/>
                                                </Border>

                                                <!-- ✅ MODERN DESIGN: Enhanced stock panel -->
                                                <Border Grid.Row="2"
                                                        Style="{StaticResource ProductStockPanelStyle}">
                                                    <TextBlock Text="{Binding Converter={StaticResource StockDisplayConverter}}"
                                                               Style="{StaticResource ProductStockTextStyle}"
                                                               HorizontalAlignment="Center"/>
                                                </Border>

                                                <!-- ✅ MODERN DESIGN: Enhanced price panel -->
                                                <Border Grid.Row="3"
                                                        Style="{StaticResource ProductPricePanelStyle}">
                                                    <!-- ✅ MODERN DESIGN: Enhanced price display with better typography -->
                                                    <TextBlock Text="{Binding SellingPrice, StringFormat={}{0:N2} DA}"
                                                               Style="{StaticResource ProductPriceTextStyle}"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                                </Border>

                                                <!-- ✅ UI FIX: Enhanced out-of-stock overlay that shows product info -->
                                                <Border Grid.RowSpan="4"
                                                        CornerRadius="12"
                                                        Panel.ZIndex="3">
                                                    <Border.Background>
                                                        <!-- ✅ UI FIX: Reduced opacity to show product info underneath -->
                                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1" Opacity="0.75">
                                                            <GradientStop Color="#1F2937" Offset="0.0"/>
                                                            <GradientStop Color="#111827" Offset="1.0"/>
                                                        </LinearGradientBrush>
                                                    </Border.Background>
                                                    <Grid>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="*"/>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                        </Grid.RowDefinitions>

                                                        <!-- ✅ UI FIX: Product name and price visible when out of stock -->
                                                        <StackPanel Grid.Row="0" Margin="8,8,8,4">
                                                            <TextBlock Text="{Binding Name}"
                                                                       Foreground="White"
                                                                       FontWeight="SemiBold"
                                                                       FontSize="12"
                                                                       TextAlignment="Center"
                                                                       TextWrapping="Wrap"
                                                                       MaxHeight="32"
                                                                       TextTrimming="CharacterEllipsis"/>
                                                            <TextBlock Text="{Binding SellingPrice, Converter={StaticResource CurrencyFormatConverter}}"
                                                                       Foreground="#E5E7EB"
                                                                       FontWeight="Medium"
                                                                       FontSize="11"
                                                                       TextAlignment="Center"
                                                                       Margin="0,2,0,0"/>
                                                        </StackPanel>

                                                        <!-- ✅ UI FIX: Out of stock indicator in center -->
                                                        <StackPanel Grid.Row="2"
                                                                    HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"
                                                                    Margin="8,4,8,8">
                                                            <materialDesign:PackIcon Kind="AlertCircleOutline"
                                                                                     Width="20" Height="20"
                                                                                     Foreground="#EF4444"
                                                                                     HorizontalAlignment="Center"
                                                                                     Margin="0,0,0,4"/>
                                                            <TextBlock Text="{DynamicResource OutOfStock}"
                                                                       Foreground="#EF4444"
                                                                       FontWeight="Bold"
                                                                       HorizontalAlignment="Center"
                                                                       FontSize="10"
                                                                       Margin="0,0,0,8"/>

                                                            <!-- Enhanced reservation button with clearer labeling -->
                                                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                                                    Background="#3B82F6"
                                                                    BorderBrush="#2563EB"
                                                                    Foreground="White"
                                                                    FontSize="8"
                                                                    Height="24"
                                                                    MinWidth="60"
                                                                    Padding="10,3"
                                                                    Click="CreateInvoiceFromProduct_Click"
                                                                    Tag="{Binding}"
                                                                    ToolTip="Create a reservation invoice for this out-of-stock product"
                                                                    Visibility="{Binding DataContext.CanCreateInvoices, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                                <StackPanel Orientation="Horizontal">
                                                                    <materialDesign:PackIcon Kind="CalendarClock"
                                                                                           Width="10" Height="10"
                                                                                           VerticalAlignment="Center"
                                                                                           Margin="0,0,4,0"/>
                                                                    <TextBlock Text="Reserve"
                                                                             VerticalAlignment="Center"
                                                                             FontSize="8"
                                                                             FontWeight="Medium"/>
                                                                </StackPanel>
                                                            </Button>
                                                        </StackPanel>
                                                    </Grid>
                                                    <Border.Style>
                                                        <Style TargetType="Border">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsOutOfStock}" Value="True">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                </Border>

                                                <!-- ✅ MODERN DESIGN: Enhanced details button -->
                                                <Button Style="{StaticResource ProductActionButtonStyle}"
                                                        HorizontalAlignment="Right"
                                                        VerticalAlignment="Bottom"
                                                        Margin="0,0,8,8"
                                                        Panel.ZIndex="2"
                                                        Tag="{Binding}"
                                                        Click="ProductGridDetails_Click"
                                                        ToolTip="View product details">
                                                    <materialDesign:PackIcon Kind="InformationOutline"
                                                                             Width="14" Height="14"
                                                                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                                </Button>


                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>

                        <!-- ✅ FILTER FIX: Empty State Overlay for when no products are found -->
                        <Grid HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Margin="20">
                            <Grid.Style>
                                <Style TargetType="Grid">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <!-- Show when not loading and no products found -->
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding IsLoading}" Value="False"/>
                                                <Condition Binding="{Binding FilteredProducts.Count}" Value="0"/>
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Grid.Style>
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="PackageVariantClosed"
                                                         Width="64" Height="64"
                                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                         HorizontalAlignment="Center"
                                                         Margin="0,0,0,16"/>
                                <TextBlock Text="{DynamicResource NoProductsFound}"
                                           FontSize="18"
                                           FontWeight="Medium"
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,8"/>
                                <TextBlock Text="{DynamicResource NoProductsFoundHint}"
                                           FontSize="14"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                           HorizontalAlignment="Center"
                                           TextAlignment="Center"
                                           TextWrapping="Wrap"
                                           MaxWidth="300"/>
                            </StackPanel>
                        </Grid>

                        <!-- Main Loading Indicator -->
                        <materialDesign:Card
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      Background="{DynamicResource MaterialDesignCardBackground}"
                                      Margin="0,0,0,0"
                                      Padding="24,16"
                                      UniformCornerRadius="8"
                                      materialDesign:ElevationAssist.Elevation="Dp4"
                                      Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       IsIndeterminate="True"
                                       Value="0"
                                       Width="36"
                                       Height="36"
                                       Margin="0,0,0,12" />
                                <TextBlock Text="Loading products..."
                                     HorizontalAlignment="Center"
                                     FontSize="16" />
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Scroll Loading Indicator -->
                        <materialDesign:Card x:Name="loadingIndicator"
                                      Visibility="Collapsed"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Bottom"
                                      Background="{DynamicResource MaterialDesignCardBackground}"
                                      Margin="0,0,0,16"
                                      Padding="8"
                                      UniformCornerRadius="4"
                                      materialDesign:ElevationAssist.Elevation="Dp3">
                            <StackPanel Orientation="Horizontal">
                                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       IsIndeterminate="True"
                                       Value="0"
                                       Width="20"
                                       Height="20"
                                       Margin="0,0,8,0" />
                                <TextBlock Text="Loading more products..."
                                     VerticalAlignment="Center"
                                     FontSize="14" />
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </Grid>

                <!-- Middle Column - Quick Action Buttons -->
                <StackPanel Grid.Column="2" Width="60" Margin="0,8,8,8" VerticalAlignment="Top">
                    <!-- Barcode Search Button (just enhancing the UI for example) -->
                    <materialDesign:Card Background="{DynamicResource PrimaryHueDarkBrush}"
                                  UniformCornerRadius="12"
                                  materialDesign:ElevationAssist.Elevation="Dp3"
                                  Margin="0,0,0,12"
                                  Cursor="Hand">
                        <!-- Removed barcode search button shadow for perf -->
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                          Background="Transparent"
                          Foreground="{DynamicResource MaterialDesignPaper}"
                          Width="60"
                          Height="60"
                          Padding="0"
                          Click="BarcodeSearch_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Barcode"
                                                   Width="22"
                                                   Height="22"
                                                   HorizontalAlignment="Center"/>
                                <TextBlock Text="{DynamicResource BarcodeSearch}"
                                     HorizontalAlignment="Center"
                                     TextWrapping="Wrap"
                                     TextAlignment="Center"
                                     FontSize="10"
                                     Margin="0,4,0,0"/>
                            </StackPanel>
                        </Button>
                    </materialDesign:Card>

                    <!-- Cart Discount Button -->
                    <materialDesign:Card Background="{DynamicResource SystemAlertBrush}"
                                  UniformCornerRadius="8"
                                  materialDesign:ElevationAssist.Elevation="Dp2"
                                  Margin="0,0,0,8"
                                  Cursor="Hand">
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                          Background="Transparent"
                          Foreground="{DynamicResource MaterialDesignPaper}"
                          Width="60"
                          Height="60"
                          Padding="0"
                          Click="ApplyCartDiscount_Click"
                          IsEnabled="{Binding HasCartItems}">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Sale"
                                                   Width="22"
                                                   Height="22"
                                                   HorizontalAlignment="Center"/>
                                <TextBlock Text="{DynamicResource ApplyDiscount}"
                                     HorizontalAlignment="Center"
                                     TextWrapping="Wrap"
                                     TextAlignment="Center"
                                     FontSize="10"
                                     Margin="0,4,0,0"/>
                            </StackPanel>
                        </Button>
                    </materialDesign:Card>

                    <!-- New Cart Button -->
                    <materialDesign:Card Background="{DynamicResource SuccessBrush}"
                                  UniformCornerRadius="8"
                                  materialDesign:ElevationAssist.Elevation="Dp2"
                                  Margin="0,0,0,8"
                                  Cursor="Hand">
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                          Background="Transparent"
                          Foreground="{DynamicResource MaterialDesignPaper}"
                          Width="60"
                          Height="60"
                          Padding="0"
                          Click="NewCart_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="CartPlus"
                                                   Width="22"
                                                   Height="22"
                                                   HorizontalAlignment="Center"/>
                                <TextBlock Text="{DynamicResource NewCart}"
                                     HorizontalAlignment="Center"
                                     TextWrapping="Wrap"
                                     TextAlignment="Center"
                                     FontSize="10"
                                     Margin="0,4,0,0"/>
                            </StackPanel>
                        </Button>
                    </materialDesign:Card>

                    <!-- Custom Product Button -->
                    <materialDesign:Card Background="{DynamicResource SuccessBrush}"
                                  UniformCornerRadius="8"
                                  materialDesign:ElevationAssist.Elevation="Dp2"
                                  Margin="0,0,0,8"
                                  Cursor="Hand">
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                          Background="Transparent"
                          Foreground="{DynamicResource SystemBackgroundBrush}"
                          Width="60"
                          Height="60"
                          Padding="0"
                          Click="CustomProduct_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="BasketPlus"
                                                   Width="22"
                                                   Height="22"
                                                   HorizontalAlignment="Center"/>
                                <TextBlock Text="{DynamicResource CustomProduct}"
                                     HorizontalAlignment="Center"
                                     TextWrapping="Wrap"
                                     TextAlignment="Center"
                                     FontSize="10"
                                     Margin="0,4,0,0"/>
                            </StackPanel>
                        </Button>
                    </materialDesign:Card>

                    <!-- Close Cart Button -->
                    <materialDesign:Card Background="{DynamicResource SystemAlertBrush}"
                                  UniformCornerRadius="8"
                                  materialDesign:ElevationAssist.Elevation="Dp2"
                                  Margin="0,0,0,8"
                                  Cursor="Hand">
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                          Background="Transparent"
                          Foreground="{DynamicResource MaterialDesignPaper}"
                          Width="60"
                          Height="60"
                          Padding="0"
                          Click="CloseActiveCart_Click"
                          IsEnabled="{Binding CanCloseCart}">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="CartRemove"
                                                   Width="22"
                                                   Height="22"
                                                   HorizontalAlignment="Center"/>
                                <TextBlock Text="{DynamicResource CloseCart}"
                                     TextWrapping="Wrap"
                                     TextAlignment="Center"
                                     FontSize="10"/>
                            </StackPanel>
                        </Button>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Right Side - Cart Section -->
                <Grid Grid.Column="3" Margin="8,8,8,8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Sales Navigation Panel -->
                    <materialDesign:Card Grid.Row="0"
                                   Background="{DynamicResource MaterialDesignPaper}"
                                   UniformCornerRadius="8"
                                   materialDesign:ElevationAssist.Elevation="Dp2"
                                   Margin="0,0,0,8">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="32"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Top Section with Title -->
                            <Border Grid.Row="0"
                                    Background="{DynamicResource PrimaryHueLightBrush}"
                                    Padding="10,0">
                                <DockPanel VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Receipt"
                                                           Width="14"
                                                           Height="14"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="{DynamicResource SaleNavigation}"
                                             Margin="6,0,0,0"
                                             FontSize="12"
                                             VerticalAlignment="Center"/>
                                </DockPanel>
                            </Border>

                            <!-- Controls Section -->
                            <Grid Grid.Row="1" Margin="8,6,8,6">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Invoice Number Input with Icon -->
                                <Grid Grid.Column="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0"
                                            Background="{DynamicResource PrimaryHueLightBrush}"
                                            CornerRadius="4,0,0,4"
                                            Width="28"
                                            Height="28">
                                        <materialDesign:PackIcon Kind="FileDocumentOutline"
                                                               Width="14"
                                                               Height="14"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>

                                    <TextBox Grid.Column="1"
                                            x:Name="txtInvoiceNumber"
                                            materialDesign:HintAssist.Hint="{DynamicResource InvoiceNumber}"
                                            materialDesign:TextFieldAssist.HasClearButton="True"
                                            materialDesign:TextFieldAssist.PrefixText="#"
                                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                            Height="28"
                                            FontSize="12"
                                            Padding="6,2,6,0"
                                            Margin="0,0,8,0"
                                            Text="{Binding CurrentInvoiceNumber, UpdateSourceTrigger=PropertyChanged}"
                                            KeyDown="TxtInvoiceNumber_KeyDown"
                                            VerticalContentAlignment="Center"/>
                                </Grid>

                                <!-- Navigation Buttons Group -->
                                <Border Grid.Column="1"
                                        Background="{DynamicResource PrimaryHueLightBrush}"
                                        CornerRadius="14"
                                        Margin="0,0,8,0"
                                        Height="28"
                                        materialDesign:ElevationAssist.Elevation="Dp1">
                                    <StackPanel Orientation="Horizontal" Margin="2">
                                        <!-- Previous Sale Button -->
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                Width="24" Height="24"
                                                Background="{DynamicResource PrimaryHueMidBrush}"
                                                Foreground="{DynamicResource MaterialDesignPaper}"
                                                Margin="0"
                                                Padding="0"
                                                Click="PreviousSale_Click"
                                                ToolTip="{DynamicResource PreviousSale}">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="ArrowLeftBoldCircle" Width="16" Height="16"/>
                                            </StackPanel>
                                        </Button>

                                        <!-- Next Sale Button -->
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                Width="24" Height="24"
                                                Background="{DynamicResource PrimaryHueMidBrush}"
                                                Foreground="{DynamicResource MaterialDesignPaper}"
                                                Margin="2,0,0,0"
                                                Padding="0"
                                                Click="NextSale_Click"
                                                ToolTip="{DynamicResource NextSale}">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="ArrowRightBoldCircle" Width="16" Height="16"/>
                                            </StackPanel>
                                        </Button>
                                    </StackPanel>
                                </Border>

                                <!-- Save Changes Button -->
                                <Button Grid.Column="2"
                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                        Background="{DynamicResource SecondaryHueMidBrush}"
                                        Foreground="{DynamicResource SecondaryHueMidForegroundBrush}"
                                        Height="28"
                                        Margin="0,0,8,0"
                                        Padding="6,0"
                                        Click="SaveChanges_Click"
                                        Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        ToolTip="{DynamicResource SaveChanges}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ContentSave"
                                                               Width="14"
                                                               Height="14"
                                                               Margin="0,0,4,0"
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{DynamicResource SaveChanges}"
                                                  VerticalAlignment="Center"
                                                  FontSize="12"/>
                                    </StackPanel>
                                </Button>

                                <!-- Exit Edit Mode Button -->
                                <Button Grid.Column="3"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Height="28"
                                        Padding="6,0"
                                        Click="ExitEditMode_Click"
                                        Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        ToolTip="{DynamicResource ExitEditMode}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Close"
                                                               Width="14"
                                                               Height="14"
                                                               Margin="0,0,4,0"
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{DynamicResource ExitEditMode}"
                                                  VerticalAlignment="Center"
                                                  FontSize="12"/>
                                    </StackPanel>
                                </Button>
                            </Grid>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Cart List -->
                    <materialDesign:Card Grid.Row="1"
                                  Background="{DynamicResource MaterialDesignPaper}"
                                  UniformCornerRadius="10"
                                  materialDesign:ElevationAssist.Elevation="Dp2"
                                  Margin="0,0,0,8"
                                  Grid.RowSpan="2">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Cart Tabs -->
                            <Grid Grid.Row="0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Cart Chips - Compact Design Without Header -->
                                <Border Grid.Row="0"
                                   BorderThickness="0,0,0,1"
                                   BorderBrush="{DynamicResource MaterialDesignDivider}"
                                   Margin="8,8,8,2"
                                   Padding="0,0,0,2">
                                    <ScrollViewer HorizontalScrollBarVisibility="Auto"
                                            VerticalScrollBarVisibility="Disabled"
                                            MaxHeight="34">
                                        <ItemsControl ItemsSource="{Binding Carts}">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <StackPanel Orientation="Horizontal" />
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border Background="{DynamicResource MaterialDesignPaper}"
                                                   CornerRadius="14"
                                                   Margin="2,0,4,0"
                                                   Height="28"
                                                   x:Name="TabBorder"
                                                   MouseDown="Cart_Click"
                                                   Cursor="Hand"
                                                   materialDesign:ElevationAssist.Elevation="Dp1">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>

                                                            <!-- Cart Icon -->
                                                            <materialDesign:PackIcon Kind="CartOutline"
                                                                           Grid.Column="0"
                                                                           Width="14"
                                                                           Height="14"
                                                                           Margin="8,0,2,0"
                                                                           VerticalAlignment="Center"
                                                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                           x:Name="TabIcon"/>

                                                            <!-- Cart Name -->
                                                            <TextBlock Grid.Column="1"
                                                             Text="{Binding Name}"
                                                             VerticalAlignment="Center"
                                                             FontSize="12"
                                                             FontWeight="Normal"
                                                             Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                             Margin="0,0,4,0"
                                                             x:Name="TabText"/>

                                                            <!-- Close Button -->
                                                            <Button Grid.Column="2"
                                                           Style="{StaticResource MaterialDesignIconButton}"
                                                           Height="20"
                                                           Width="20"
                                                           Padding="0"
                                                           Margin="0,0,4,0"
                                                           Click="CloseCart_Click"
                                                           Tag="{Binding}"
                                                           ToolTip="{DynamicResource CloseCart}">
                                                                <materialDesign:PackIcon Kind="Close"
                                                                               Width="10"
                                                                               Height="10"
                                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                               x:Name="TabCloseIcon"/>
                                                            </Button>
                                                        </Grid>
                                                    </Border>
                                                    <DataTemplate.Triggers>
                                                        <DataTrigger Binding="{Binding IsActive}" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}" TargetName="TabBorder" />
                                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignPaper}" TargetName="TabText" />
                                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignPaper}" TargetName="TabIcon" />
                                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignPaper}" TargetName="TabCloseIcon" />
                                                            <Setter Property="FontWeight" Value="Medium" TargetName="TabText" />
                                                            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3" TargetName="TabBorder" />
                                                        </DataTrigger>
                                                    </DataTemplate.Triggers>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                        </ScrollViewer>

                                </Border>
                            </Grid>

                            <!-- Cart Items - more compact layout -->
                            <ListView Grid.Row="1"
                                ItemsSource="{Binding CurrentCart.Items}"
                                SelectedItem="{Binding SelectedCartItem, Mode=TwoWay}"
                                SelectionChanged="CartItems_SelectionChanged"
                                BorderThickness="0"
                                Background="Transparent"
                                Foreground="{DynamicResource MaterialDesignBody}"
                                FontSize="14"
                                x:Name="cartItemsListView"
                                Padding="6,4"
                                VirtualizingPanel.IsVirtualizing="True"
                                VirtualizingPanel.VirtualizationMode="Recycling"
                                ScrollViewer.IsDeferredScrollingEnabled="True"
                                ScrollViewer.CanContentScroll="True"
                                behaviors:ScrollIntoViewBehavior.AutoScrollToSelectedItem="True">
                                <ListView.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <VirtualizingStackPanel />
                                    </ItemsPanelTemplate>
                                </ListView.ItemsPanel>
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="4,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Product Info -->
                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding Product.Name}"
                                                         FontWeight="Medium"
                                                         TextTrimming="CharacterEllipsis"
                                                         FontSize="14"
                                                         Foreground="{DynamicResource MaterialDesignBody}"
                                                         Margin="0,0,4,2"/>

                                                    <!-- Service Indicator -->
                                                    <Border Background="{DynamicResource SecondaryHueLightBrush}"
                                                          CornerRadius="8"
                                                          Padding="4,1"
                                                          Margin="0,0,0,2"
                                                          Visibility="{Binding IsService, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                        <TextBlock Text="Service"
                                                                 FontSize="10"
                                                                 FontWeight="Medium"
                                                                 Foreground="{DynamicResource SecondaryHueDarkBrush}"/>
                                                    </Border>
                                                </StackPanel>

                                                <!-- Price Information -->
                                                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                                    <!-- Original Price (with strikethrough when discounted) -->
                                                    <TextBlock Margin="0,0,6,0">
                                                        <TextBlock.Style>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="Text" Value="{Binding OriginalTotal, StringFormat={}{0:N2} DA}"/>
                                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                                                <Setter Property="TextDecorations" Value="Strikethrough"/>
                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding HasDiscount}" Value="True">
                                                                        <Setter Property="Visibility" Value="Visible"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </TextBlock.Style>
                                                    </TextBlock>

                                                    <!-- Discounted/Final Price -->
                                                    <TextBlock Text="{Binding Total, StringFormat={}{0:N2} DA}"
                                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                         FontSize="13"
                                                         FontWeight="SemiBold"/>

                                                    <!-- Discount Icon and Percentage -->
                                                    <StackPanel Orientation="Horizontal" Margin="6,0,0,0">
                                                        <StackPanel.Style>
                                                            <Style TargetType="StackPanel">
                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding HasDiscount}" Value="True">
                                                                        <Setter Property="Visibility" Value="Visible"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </StackPanel.Style>
                                                        <materialDesign:PackIcon Kind="Sale"
                                                                         Width="14"
                                                                         Height="14"
                                                                         Foreground="#FF5252"
                                                                         VerticalAlignment="Center"/>
                                                        <TextBlock Text="{Binding DiscountPercentage, StringFormat={}(-{0:N0}%)}"
                                                             Foreground="#FF5252"
                                                             FontSize="11"
                                                             FontWeight="Medium"
                                                             Margin="2,0,0,0"
                                                             VerticalAlignment="Center"/>
                                                    </StackPanel>

                                                    <!-- Bulk Pricing Information -->
                                                    <StackPanel Orientation="Horizontal" Margin="6,0,0,0">
                                                        <StackPanel.Style>
                                                            <Style TargetType="StackPanel">
                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding HasBulkPricing}" Value="True">
                                                                        <Setter Property="Visibility" Value="Visible"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </StackPanel.Style>
                                                        <materialDesign:PackIcon Kind="ScaleBalance"
                                                                         Width="14"
                                                                         Height="14"
                                                                         Foreground="#4CAF50"
                                                                         VerticalAlignment="Center"/>
                                                        <TextBlock Text="{Binding BulkSavingsDisplay}"
                                                             Foreground="#4CAF50"
                                                             FontSize="11"
                                                             FontWeight="Medium"
                                                             Margin="2,0,0,0"
                                                             VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </StackPanel>

                                                <!-- Bulk Pricing Tier Display -->
                                                <TextBlock Text="{Binding BulkPricingDisplay}"
                                                         FontSize="10"
                                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                         Margin="0,2,0,0">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding HasBulkPricing}" Value="True">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </StackPanel>

                                            <!-- Quantity Controls -->
                                            <materialDesign:Card Grid.Column="1"
                                                          Background="{DynamicResource PrimaryHueMidBrush}"
                                                          UniformCornerRadius="16"
                                                          materialDesign:ElevationAssist.Elevation="Dp1"
                                                          Margin="0,0,8,0">
                                                <StackPanel Orientation="Horizontal"
                                                      Margin="2,1">
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      Width="28"
                                                      Height="28"
                                                      Background="Transparent"
                                                      Foreground="{DynamicResource MaterialDesignPaper}"
                                                      Click="DecreaseQuantity_Click"
                                                      Tag="{Binding}">
                                                        <materialDesign:PackIcon Kind="Minus" Width="16" Height="16"/>
                                                    </Button>

                                                    <TextBlock Text="{Binding Quantity}"
                                                         Width="24"
                                                         TextAlignment="Center"
                                                         VerticalAlignment="Center"
                                                         FontSize="15"
                                                         FontWeight="SemiBold"
                                                         Foreground="{DynamicResource MaterialDesignPaper}"/>

                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      Width="28"
                                                      Height="28"
                                                      Background="Transparent"
                                                      Foreground="{DynamicResource MaterialDesignPaper}"
                                                      Click="IncreaseQuantity_Click"
                                                      Tag="{Binding}">
                                                        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"/>
                                                    </Button>
                                                </StackPanel>
                                            </materialDesign:Card>

                                            <!-- Set Quantity Button -->
                                            <Button Grid.Column="2"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Width="28"
                                              Height="28"
                                              Background="{DynamicResource PrimaryHueMidBrush}"
                                              Click="SetQuantity_Click"
                                              Tag="{Binding}"
                                              materialDesign:ElevationAssist.Elevation="Dp1"
                                              Margin="0,0,4,0"
                                              ToolTip="{DynamicResource SetQuantity}">
                                                <materialDesign:PackIcon Kind="Pencil"
                                                                   Width="16"
                                                                   Height="16"
                                                                   Foreground="{DynamicResource MaterialDesignPaper}"/>
                                            </Button>

                                            <!-- Item Discount Button -->
                                            <Button Grid.Column="3"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Width="28"
                                              Height="28"
                                              Background="{DynamicResource PrimaryHueMidBrush}"
                                              Click="ApplyItemDiscount_Click"
                                              Tag="{Binding}"
                                              materialDesign:ElevationAssist.Elevation="Dp1"
                                              Margin="0,0,4,0"
                                              ToolTip="{DynamicResource ApplyDiscount}">
                                                <materialDesign:PackIcon Kind="Sale"
                                                                   Width="16"
                                                                   Height="16"
                                                                   Foreground="{DynamicResource MaterialDesignPaper}"/>
                                            </Button>

                                            <!-- Delete Button -->
                                            <Button Grid.Column="4"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Width="28"
                                              Height="28"
                                              Background="{DynamicResource PrimaryHueMidBrush}"
                                              Click="RemoveItem_Click"
                                              Tag="{Binding}"
                                              materialDesign:ElevationAssist.Elevation="Dp1"
                                              ToolTip="{DynamicResource RemoveItem}">
                                                <materialDesign:PackIcon Kind="Delete"
                                                                   Width="16"
                                                                   Height="16"
                                                                   Foreground="{DynamicResource MaterialDesignPaper}"/>
                                            </Button>
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem" BasedOn="{StaticResource MaterialDesignListBoxItem}">
                                        <Setter Property="Padding" Value="4"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
                                    </Style>
                                </ListView.ItemContainerStyle>
                            </ListView>

                        </Grid>
                    </materialDesign:Card>
                </Grid>
            </Grid>

            <!-- Function Keys / Shortcuts -->
            <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" materialDesign:ElevationAssist.Elevation="Dp3" Padding="8,4">
                <WrapPanel HorizontalAlignment="Center">
                    <materialDesign:Chip Background="{DynamicResource MaterialDesignPaper}" Margin="3,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="F3" Foreground="{DynamicResource MaterialDesignBody}" FontWeight="Bold" Margin="0,0,2,0"/>
                            <TextBlock Text="{DynamicResource Search}" Foreground="{DynamicResource MaterialDesignBody}"/>
                        </StackPanel>
                    </materialDesign:Chip>

                    <materialDesign:Chip Background="{DynamicResource MaterialDesignPaper}" Margin="3,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="F4" Foreground="{DynamicResource MaterialDesignBody}" FontWeight="Bold" Margin="0,0,2,0"/>
                            <TextBlock Text="{DynamicResource Payment}" Foreground="{DynamicResource MaterialDesignBody}"/>
                        </StackPanel>
                    </materialDesign:Chip>

                    <materialDesign:Chip Background="{DynamicResource SecondaryHueMidBrush}" Margin="3,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="Esc" Foreground="{DynamicResource MaterialDesignPaper}" FontWeight="Bold" Margin="0,0,2,0"/>
                            <TextBlock Text="{DynamicResource Cancel}" Foreground="{DynamicResource MaterialDesignPaper}"/>
                        </StackPanel>
                    </materialDesign:Chip>
                </WrapPanel>
            </materialDesign:ColorZone>

            <!-- Add Keyboard Shortcuts Panel at the bottom -->
            <Border Grid.Row="2" Grid.Column="1" Padding="3,1" Margin="6,0,6,3"
                Background="{DynamicResource MaterialDesignPaper}"
                BorderBrush="{DynamicResource PrimaryHueLightBrush}" BorderThickness="1"
                CornerRadius="4" VerticalAlignment="Bottom"
                HorizontalAlignment="Left"
                MaxWidth="350">
                <Expander Header="{DynamicResource KeyboardShortcuts}" IsExpanded="False">
                    <Expander.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}" FontSize="11" Margin="0"/>
                        </DataTemplate>
                    </Expander.HeaderTemplate>
                    <Grid Margin="6,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Column 1 -->
                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="F2" FontWeight="Bold"/> - <Run Text="{DynamicResource ProductDetails}"/>
                            </TextBlock>
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="F3" FontWeight="Bold"/> - <Run Text="{DynamicResource Search}"/>
                            </TextBlock>
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="F4" FontWeight="Bold"/> - <Run Text="{DynamicResource ProcessPayment}"/>
                            </TextBlock>
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="F5" FontWeight="Bold"/> - <Run Text="{DynamicResource RedeemPoints}"/>
                            </TextBlock>
                        </StackPanel>

                        <!-- Column 2 -->
                        <StackPanel Grid.Column="1" Margin="0,0,12,0">
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="F6" FontWeight="Bold"/> - <Run Text="{DynamicResource CartDiscount}"/>
                            </TextBlock>
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="F7" FontWeight="Bold"/> - <Run Text="{DynamicResource ItemDiscount}"/>
                            </TextBlock>
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="F8" FontWeight="Bold"/> - <Run Text="{DynamicResource SetQuantity}"/>
                            </TextBlock>
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="F9" FontWeight="Bold"/> - <Run Text="{DynamicResource LookupCustomer}"/>
                            </TextBlock>
                        </StackPanel>

                        <!-- Column 3 -->
                        <StackPanel Grid.Column="2" Margin="0">
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="F12" FontWeight="Bold"/> - <Run Text="{DynamicResource NewCart}"/>
                            </TextBlock>
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="+" FontWeight="Bold"/> - <Run Text="{DynamicResource IncreaseQuantity}"/>
                            </TextBlock>
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="-" FontWeight="Bold"/> - <Run Text="{DynamicResource DecreaseQuantity}"/>
                            </TextBlock>
                            <TextBlock Margin="0,1" FontSize="10">
                            <Run Text="Delete" FontWeight="Bold"/> - <Run Text="{DynamicResource RemoveItem}"/>
                            </TextBlock>
                        </StackPanel>
                    </Grid>
                </Expander>
            </Border>
        </Grid>
    </materialDesign:DialogHost>
</UserControl>
