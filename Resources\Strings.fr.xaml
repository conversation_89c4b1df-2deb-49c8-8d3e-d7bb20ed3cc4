<?xml version="1.0" encoding="utf-8"?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    
    <!-- Common -->
    <system:String x:Key="AppName">Système de Point de Vente</system:String>
    <system:String x:Key="Loading">Chargement...</system:String>
    <system:String x:Key="Save">Enregistrer</system:String>
    <system:String x:Key="Cancel">Annuler</system:String>
    <system:String x:Key="Delete">Supprimer</system:String>
    <system:String x:Key="Edit">Modifier</system:String>
    <system:String x:Key="Add">Ajouter</system:String>
    <system:String x:Key="Search">Rechercher</system:String>
    <system:String x:Key="Clear">Effacer</system:String>
    <system:String x:Key="Close">Fermer</system:String>
    <system:String x:Key="Confirm">Confirmer</system:String>
    <system:String x:Key="Print">Imprimer</system:String>
    <system:String x:Key="Export">Exporter</system:String>
    <system:String x:Key="Import">Importer</system:String>
    <system:String x:Key="Refresh">Actualiser</system:String>
    <system:String x:Key="Filter">Filtrer</system:String>
    <system:String x:Key="Sort">Trier</system:String>
    <system:String x:Key="Details">Détails</system:String>
    <system:String x:Key="Status">Statut</system:String>
    <system:String x:Key="Actions">Actions</system:String>
    <system:String x:Key="OK">OK</system:String>
    <system:String x:Key="Value">Valeur</system:String>

    <!-- Missing Navigation Resources -->
    <system:String x:Key="CustomProduct">Produit Personnalisé</system:String>
    <system:String x:Key="SaleNavigation">Navigation des Ventes</system:String>
    <system:String x:Key="PreviousSale">Vente Précédente</system:String>
    <system:String x:Key="NextSale">Vente Suivante</system:String>
    <system:String x:Key="SaveChanges">Enregistrer les Modifications</system:String>
    <system:String x:Key="ExitEditMode">Quitter le Mode Édition</system:String>
    <system:String x:Key="Payment">Paiement</system:String>
    <system:String x:Key="TodaysSales">Ventes du jour</system:String>
    <system:String x:Key="BusinessOverview">Aperçu de l'activité</system:String>
    <system:String x:Key="NoData">Aucune donnée</system:String>
    
    <!-- Navigation -->
    <system:String x:Key="Dashboard">Tableau de Bord</system:String>
    <system:String x:Key="Sales">Ventes</system:String>
    <system:String x:Key="Purchase">Achats</system:String>
    <system:String x:Key="Products">Produits</system:String>
    <system:String x:Key="Categories">Catégories</system:String>
    <system:String x:Key="Customers">Clients</system:String>
    <system:String x:Key="Suppliers">Fournisseurs</system:String>
    <system:String x:Key="Users">Utilisateurs</system:String>
    <system:String x:Key="Reports">Rapports</system:String>
    <system:String x:Key="Settings">Paramètres</system:String>
    <system:String x:Key="Logout">Déconnexion</system:String>

    <!-- Products View -->
    <system:String x:Key="SelectProductGeneral">Sélectionner un produit</system:String>
    <system:String x:Key="SelectProductForStats">Sélectionner un produit pour les statistiques</system:String>

    <!-- Stats Details Dialog -->
    <system:String x:Key="Analysis">Analyse</system:String>
    <system:String x:Key="DetailedMetricsAndTrends">Métriques et tendances détaillées</system:String>
    <system:String x:Key="AnalysisFor">Analyse pour</system:String>
    <system:String x:Key="TimePeriod">Période</system:String>
    <system:String x:Key="FilterByCategoryStats">Filtrer par catégorie</system:String>
    <system:String x:Key="FilterByProduct">Filtrer par produit</system:String>
    <system:String x:Key="SelectCategory">Sélectionner une catégorie</system:String>
    <system:String x:Key="SalesTrend">Tendance des ventes</system:String>
    <system:String x:Key="SalesByHour">Ventes par heure</system:String>
    <system:String x:Key="SalesByDayOfWeek">Ventes par jour de la semaine</system:String>
    <system:String x:Key="TopProducts">Meilleurs produits</system:String>

    <!-- Time Periods -->
    <system:String x:Key="TimePeriod_Today">Aujourd'hui</system:String>
    <system:String x:Key="TimePeriod_Yesterday">Hier</system:String>
    <system:String x:Key="TimePeriod_Week">Semaine</system:String>
    <system:String x:Key="TimePeriod_Month">Mois</system:String>
    <system:String x:Key="TimePeriod_Year">Année</system:String>
    <system:String x:Key="TimePeriod_Quarter">Trimestre</system:String>
    <system:String x:Key="TimePeriod_Custom">Personnalisé</system:String>
    <system:String x:Key="TimePeriod_Last7Days">7 derniers jours</system:String>
    <system:String x:Key="TimePeriod_Last30Days">30 derniers jours</system:String>
    <system:String x:Key="TimePeriod_Last90Days">90 derniers jours</system:String>
    <system:String x:Key="TimePeriod_ThisMonth">Ce mois</system:String>
    <system:String x:Key="TimePeriod_LastMonth">Mois dernier</system:String>
    <system:String x:Key="TimePeriod_ThisYear">Cette année</system:String>
    <system:String x:Key="TimePeriod_ThisWeek">Cette semaine</system:String>

    <!-- Chart Labels -->
    <system:String x:Key="Hour">Heure</system:String>
    <system:String x:Key="Quantity">Quantité</system:String>
    <system:String x:Key="Amount">Montant</system:String>
    <system:String x:Key="Date">Date</system:String>
    <system:String x:Key="Time">Temps</system:String>
    <system:String x:Key="Period">Période</system:String>

    <!-- Product Management -->
    <system:String x:Key="ProductName">Nom du produit</system:String>
    <system:String x:Key="ProductCode">Code produit</system:String>
    <system:String x:Key="Barcode">Code-barres</system:String>
    <system:String x:Key="Price">Prix</system:String>
    <system:String x:Key="Cost">Coût</system:String>
    <system:String x:Key="StockQuantity">Quantité en stock</system:String>
    <system:String x:Key="MinimumStock">Stock minimum</system:String>
    <system:String x:Key="Active">Actif</system:String>
    <system:String x:Key="Inactive">Inactif</system:String>
    <system:String x:Key="LastUpdated">Dernière mise à jour</system:String>

    <!-- Dashboard -->
    <system:String x:Key="Revenue">Revenus</system:String>
    <system:String x:Key="Profit">Profit</system:String>
    <system:String x:Key="ItemsSold">Articles Vendus</system:String>
    <system:String x:Key="Category">Catégorie</system:String>
    <system:String x:Key="FilterByCategory">Filtrer par Catégorie</system:String>
    <system:String x:Key="AllCategories">Toutes les Catégories</system:String>
    <system:String x:Key="Product">Produit</system:String>
    <system:String x:Key="SelectProduct">Sélectionner un Produit</system:String>
    <system:String x:Key="AllProducts">Tous les Produits</system:String>
    <system:String x:Key="SelectCategoryStats">Sélectionner une Catégorie</system:String>
    <system:String x:Key="AllCategoriesStats">Toutes les Catégories</system:String>
    <system:String x:Key="SelectProductStats">Sélectionner un Produit</system:String>
    <system:String x:Key="AllProductsStats">Tous les Produits</system:String>
    <system:String x:Key="TotalSales">Ventes Totales</system:String>
    <system:String x:Key="TotalSalesDescription">Montant total des ventes</system:String>
    <system:String x:Key="RevenueDescription">Revenus totaux</system:String>
    <system:String x:Key="GrossProfitDescription">Bénéfice brut avant dépenses</system:String>
    <system:String x:Key="ProfitDescription">Bénéfice net</system:String>
    
    <!-- Profit Statistics -->
    <system:String x:Key="ProfitStatsTitle">Statistiques des Bénéfices</system:String>
    <system:String x:Key="ProfitStatsFor">Statistiques des Bénéfices pour</system:String>
    <system:String x:Key="TotalProfit">Bénéfice Total</system:String>
    <system:String x:Key="ProfitMargin">Marge Bénéficiaire</system:String>
    <system:String x:Key="AverageProfit">Bénéfice Moyen</system:String>
    <system:String x:Key="ProfitTrend">Tendance des Bénéfices</system:String>
    <system:String x:Key="HourlyDistribution">Distribution Horaire</system:String>
    <system:String x:Key="DailyDistribution">Distribution Journalière</system:String>
    <system:String x:Key="GrossProfit">Bénéfice Brut</system:String>
    <system:String x:Key="NetProfit">Bénéfice Net</system:String>
    <system:String x:Key="ProfitType">Type de Bénéfice</system:String>
    <system:String x:Key="SelectPeriod">Sélectionner la Période</system:String>
    <system:String x:Key="ProfitByHour">Bénéfice par Heure</system:String>
    <system:String x:Key="ProfitByDay">Bénéfice par Jour</system:String>
    <system:String x:Key="TopPerformingProducts">Produits les Plus Performants</system:String>
    <system:String x:Key="ProductQuantity">Quantité</system:String>
    <system:String x:Key="ProductProfit">Bénéfice</system:String>
    <system:String x:Key="ProductTotalSales">Ventes Totales</system:String>

    <!-- Services -->
    <system:String x:Key="Service">Service</system:String>
    <system:String x:Key="Services">Services</system:String>
    <system:String x:Key="ServiceName">Nom du Service</system:String>
    <system:String x:Key="ServiceDescription">Description du Service</system:String>
    <system:String x:Key="ServiceQuantity">Quantité de Service</system:String>
    <system:String x:Key="ServicePrice">Prix du Service</system:String>
    <system:String x:Key="ServiceCost">Coût du Service</system:String>
    <system:String x:Key="AddService">Ajouter un Service</system:String>
    <system:String x:Key="EditService">Modifier le Service</system:String>
    <system:String x:Key="SelectService">Sélectionner un Service</system:String>
    <system:String x:Key="ServiceType">Type de Service</system:String>
    <system:String x:Key="ProductType">Type de Produit</system:String>
    <system:String x:Key="ItemType">Type d'Article</system:String>

    <!-- Dashboard Card Titles - Sales -->
    <system:String x:Key="SalesCard_TodaysSales">Ventes d'Aujourd'hui</system:String>
    <system:String x:Key="SalesCard_YesterdaysSales">Ventes d'Hier</system:String>
    <system:String x:Key="SalesCard_ThisWeeksSales">Ventes de Cette Semaine</system:String>
    <system:String x:Key="SalesCard_ThisMonthsSales">Ventes de Ce Mois</system:String>
    <system:String x:Key="SalesCard_ThisYearsSales">Ventes de Cette Année</system:String>
    <system:String x:Key="SalesCard_Sales">Ventes</system:String>

    <!-- Dashboard Card Titles - Profit -->
    <system:String x:Key="ProfitCard_TodaysProfit">Bénéfice d'Aujourd'hui</system:String>
    <system:String x:Key="ProfitCard_YesterdaysProfit">Bénéfice d'Hier</system:String>
    <system:String x:Key="ProfitCard_ThisWeeksProfit">Bénéfice de Cette Semaine</system:String>
    <system:String x:Key="ProfitCard_ThisMonthsProfit">Bénéfice de Ce Mois</system:String>
    <system:String x:Key="ProfitCard_ThisYearsProfit">Bénéfice de Cette Année</system:String>
    <system:String x:Key="ProfitCard_GrossProfit">Bénéfice Brut</system:String>

    <!-- Enhanced Sales Detail Dialog -->
    <system:String x:Key="TransactionInformation">Informations de Transaction</system:String>
    <system:String x:Key="FinancialSummary">Résumé Financier</system:String>
    <system:String x:Key="ReprintReceipt">Réimprimer le Reçu</system:String>
    <system:String x:Key="PreviewReceipt">Aperçu du Reçu</system:String>
    <system:String x:Key="SaveAsPDF">Enregistrer en PDF</system:String>
    <system:String x:Key="ReprintReceiptTooltip">Imprimer une copie de ce reçu</system:String>
    <system:String x:Key="PreviewReceiptTooltip">Aperçu du reçu avant impression</system:String>
    <system:String x:Key="SaveAsPDFTooltip">Enregistrer le reçu en fichier PDF</system:String>

    <!-- Regional Configuration -->
    <system:String x:Key="RegionalConfiguration">Configuration Régionale</system:String>
    <system:String x:Key="RegionalConfigurationDescription">Configurer les formats de date, de devise et de numérotation</system:String>
    <system:String x:Key="FormatPreview">Aperçu des Formats</system:String>
    <system:String x:Key="FormatPreviewDescription">Voir comment les dates, nombres et devises seront affichés</system:String>
    <system:String x:Key="DateFormatLabel">Format de Date :</system:String>
    <system:String x:Key="CurrencyFormatLabel">Format de Devise :</system:String>
    <system:String x:Key="NumberFormatLabel">Format de Nombre :</system:String>
    <system:String x:Key="CurrencySymbol">DA</system:String>
    <system:String x:Key="CurrencyFormat">{0:N2} DA</system:String>

    <!-- Theme Settings -->
    <system:String x:Key="ThemeAndAppearanceSettings">Paramètres de Thème et d'Apparence</system:String>
    <system:String x:Key="ThemeModeLabel">Mode de Thème</system:String>
    <system:String x:Key="ThemePresetsLabel">Préréglages de Thème</system:String>
    <system:String x:Key="ColorCustomizationLabel">Personnalisation des Couleurs</system:String>
    <system:String x:Key="CustomColorLabel">Couleur Personnalisée</system:String>
    <system:String x:Key="QuickColorPresetsLabel">Préréglages de Couleurs Rapides</system:String>
    <system:String x:Key="LivePreview">Aperçu en Direct</system:String>
    <system:String x:Key="LivePreviewDescription">Voir à quoi ressembleront vos changements de thème</system:String>
    <system:String x:Key="SamplePOSInterface">Interface POS d'Exemple</system:String>
    <system:String x:Key="SampleProductName">Nom du Produit : Article d'Exemple</system:String>
    <system:String x:Key="SamplePrice">Prix : 19,99 €</system:String>
    <system:String x:Key="AddToCart">Ajouter au Panier</system:String>
    <system:String x:Key="Available">Disponible :</system:String>
    <system:String x:Key="SearchProductsHint">Rechercher des produits...</system:String>
    <system:String x:Key="PrimaryThemeColorLabel">Couleur de Thème Principale</system:String>
    <system:String x:Key="CurrentThemeInfoLabel">Informations du Thème Actuel</system:String>
    <system:String x:Key="ModeLabel">Mode :</system:String>
    <system:String x:Key="PresetLabel">Préréglage :</system:String>
    <system:String x:Key="LayoutLabel">Disposition :</system:String>

    <!-- Company Settings -->
    <system:String x:Key="CompanyInformation">Informations de l'Entreprise</system:String>
    <system:String x:Key="CompanyInformationDescription">Configurez les détails de votre entreprise, les informations de contact et l'image de marque pour les reçus et factures</system:String>
    <system:String x:Key="BasicInformation">Informations de Base</system:String>
    <system:String x:Key="AddressInformation">Informations d'Adresse</system:String>
    <system:String x:Key="CompanyLogo">Logo de l'Entreprise</system:String>
    <system:String x:Key="CompanyLogoDescription">Téléchargez le logo de votre entreprise pour les reçus et factures</system:String>
    <system:String x:Key="NoLogo">Aucun Logo</system:String>
    <system:String x:Key="UploadLogo">Télécharger le Logo</system:String>
    <system:String x:Key="RemoveLogo">Supprimer le Logo</system:String>

    <!-- Database Settings -->
    <system:String x:Key="DatabaseManagement">Gestion de Base de Données</system:String>
    <system:String x:Key="DatabaseManagementDescription">Gérez l'emplacement de la base de données, créez des sauvegardes et restaurez les données pour sécuriser vos informations commerciales</system:String>
    <system:String x:Key="DatabaseLocationLabel">Emplacement de la Base de Données</system:String>
    <system:String x:Key="BackupAndRestore">Sauvegarde et Restauration</system:String>
    <system:String x:Key="BackupAndRestoreDescription">Créez des sauvegardes de vos données et restaurez à partir de sauvegardes précédentes</system:String>
    <system:String x:Key="CreateBackup">Créer une Sauvegarde</system:String>
    <system:String x:Key="CreateBackupDescription">Exportez votre base de données actuelle vers un fichier de sauvegarde</system:String>
    <system:String x:Key="RestoreBackup">Restaurer la Sauvegarde</system:String>
    <system:String x:Key="RestoreBackupDescription">Importez des données à partir d'un fichier de sauvegarde créé précédemment</system:String>
    <system:String x:Key="ImportantInformation">Informations Importantes</system:String>

    <!-- Receipt Printing Settings -->
    <system:String x:Key="ReceiptPrintingSettingsLabel">Paramètres d'Impression des Reçus</system:String>
    <system:String x:Key="GeneralSettingsLabel">Paramètres Généraux</system:String>
    <system:String x:Key="AutoPrintReceiptsLabel">Imprimer automatiquement les reçus après la finalisation de la vente</system:String>
    <system:String x:Key="ShowPrintDialogLabel">Afficher la boîte de dialogue d'impression avant l'impression</system:String>
    <system:String x:Key="SaveReceiptsAsPDFLabel">Enregistrer les reçus en sauvegarde PDF</system:String>
    <system:String x:Key="EnablePrintPreviewLabel">Activer la fonctionnalité d'aperçu avant impression</system:String>
    <system:String x:Key="PDFBackupDirectoryLabel">Répertoire de Sauvegarde PDF</system:String>
    <system:String x:Key="Browse">Parcourir</system:String>
    <system:String x:Key="PrinterConfigurationLabel">Configuration de l'Imprimante</system:String>
    <system:String x:Key="DefaultPrinterLabel">Imprimante par Défaut</system:String>
    <system:String x:Key="PrinterTypeLabel">Type d'Imprimante</system:String>
    <system:String x:Key="PaperSizeLabel">Taille du Papier</system:String>
    <system:String x:Key="StandardPrinter">Imprimante Standard</system:String>
    <system:String x:Key="ThermalPrinter">Imprimante Thermique</system:String>
    <system:String x:Key="PDFExport">Export PDF</system:String>
    <system:String x:Key="A4">A4</system:String>
    <system:String x:Key="Letter">Lettre</system:String>
    <system:String x:Key="Thermal80mm">Thermique 80mm</system:String>
    <system:String x:Key="Thermal58mm">Thermique 58mm</system:String>
    <system:String x:Key="ReceiptTemplateLabel">Modèle de Reçu</system:String>
    <system:String x:Key="IncludeCompanyLogo">Inclure le logo de l'entreprise</system:String>
    <system:String x:Key="IncludeCompanyInformation">Inclure les informations de l'entreprise</system:String>
    <system:String x:Key="IncludeCustomerInformation">Inclure les informations du client</system:String>
    <system:String x:Key="IncludeItemDetails">Inclure les détails des articles</system:String>
    <system:String x:Key="IncludePaymentInformation">Inclure les informations de paiement</system:String>
    <system:String x:Key="FontSizeLabel">Taille de Police</system:String>
    <system:String x:Key="FooterTextLabel">Texte de Pied de Page</system:String>
    <system:String x:Key="TestPrintingLabel">Test d'Impression</system:String>
    <system:String x:Key="TestPrintingDescription">Testez votre configuration d'impression de reçus avec des données d'exemple</system:String>
    <system:String x:Key="TestPrint">Test d'Impression</system:String>
    <system:String x:Key="PreviewTest">Aperçu de Test</system:String>
    <system:String x:Key="SaveTestPDF">Enregistrer PDF de Test</system:String>
    <system:String x:Key="TestConfiguration">Tester la Configuration</system:String>

    <!-- Discount Permissions -->
    <system:String x:Key="DiscountPermissionsManagement">Gestion des Autorisations de Remise</system:String>
    <system:String x:Key="DiscountPermissionsManagementDescription">Configurez les limites de remise et les exigences d'approbation pour différents rôles d'utilisateur</system:String>
    <system:String x:Key="SelectUserRole">Sélectionner le Rôle Utilisateur</system:String>
    <system:String x:Key="DiscountTypePermissions">Autorisations de Type de Remise</system:String>
    <system:String x:Key="LoadingDiscountPermissions">Chargement des autorisations de remise...</system:String>
    <system:String x:Key="ChooseRoleToConfigureHint">Choisir un rôle à configurer</system:String>
    <system:String x:Key="SelectRoleDescription">Sélectionnez un rôle d'utilisateur pour voir et modifier ses autorisations de remise. Chaque rôle peut avoir des limites différentes pour divers types de remise.</system:String>

    <!-- Discount Types -->
    <system:String x:Key="DiscountType">Type de Remise</system:String>
    <system:String x:Key="Percentage">Pourcentage</system:String>
    <system:String x:Key="FixedAmount">Montant Fixe</system:String>
    <system:String x:Key="PriceOverride">Remplacement de Prix</system:String>

    <!-- DataGrid Headers -->
     <system:String x:Key="MaxPercentage">% Max</system:String>
    <system:String x:Key="MaxAmount">Montant Max</system:String>
    <system:String x:Key="MinPricePercentage">% Prix Min</system:String>
    <system:String x:Key="Approval">Approbation</system:String>
    <system:String x:Key="Threshold">Seuil</system:String>

    <!-- Tooltips -->
    <system:String x:Key="EnableDisableDiscountTypeTooltip">Activer/Désactiver ce type de remise</system:String>
    <system:String x:Key="MaxPercentageTooltip">Pourcentage de remise maximum autorisé</system:String>
    <system:String x:Key="MaxFixedAmountTooltip">Montant fixe de remise maximum autorisé</system:String>
    <system:String x:Key="MinPricePercentageTooltip">Pourcentage de prix minimum après remise</system:String>
    <system:String x:Key="RequireApprovalTooltip">Exiger l'approbation du gestionnaire pour cette remise</system:String>
    <system:String x:Key="ApprovalThresholdTooltip">Seuil de montant qui nécessite une approbation</system:String>

    <!-- Configuration Guide -->
    <system:String x:Key="ConfigurationGuide">Guide de Configuration :</system:String>
    <system:String x:Key="MaxPercentageGuide">% Max : Pourcentage de remise maximum que ce rôle peut appliquer</system:String>
    <system:String x:Key="MaxAmountGuide">Montant Max : Montant fixe de remise maximum que ce rôle peut appliquer</system:String>
    <system:String x:Key="MinPriceGuide">% Prix Min : Pourcentage minimum du prix original qui doit rester</system:String>
    <system:String x:Key="ApprovalGuide">Approbation : Si les remises au-dessus du seuil nécessitent l'approbation du gestionnaire</system:String>
    <system:String x:Key="ThresholdGuide">Seuil : Montant au-dessus duquel l'approbation est requise</system:String>

    <!-- Loyalty Program -->
    <system:String x:Key="LoyaltyPrograms">Programmes de Fidélité</system:String>
    <system:String x:Key="ProgramTiers">Niveaux du Programme</system:String>
    <system:String x:Key="PointsPerDA">Points par DA : </system:String>
    <system:String x:Key="ValuePerPointDA"> | Valeur par point : DA</system:String>
    <system:String x:Key="Separator"> | </system:String>
    <system:String x:Key="MinPoints">Points Min : </system:String>
    <system:String x:Key="Multiplier"> | Multiplicateur : </system:String>

    <!-- Program Form Fields -->
    <system:String x:Key="ProgramNameHint">Nom du Programme</system:String>
    <system:String x:Key="DescriptionHint">Description</system:String>
    <system:String x:Key="PointsPerDollarHint">Points par Dollar</system:String>
    <system:String x:Key="ValuePerPointHint">Valeur par Point (DA)</system:String>
    <system:String x:Key="PointsExpiryHint">Expiration des Points (mois)</system:String>
    <system:String x:Key="MinimumPointsRedemptionHint">Points Minimum pour Échange</system:String>
    <system:String x:Key="ProgramActive">Programme Actif</system:String>

    <!-- Program Buttons -->
    <system:String x:Key="SaveProgram">Enregistrer le Programme</system:String>
    <system:String x:Key="AddNewProgram">Ajouter un Nouveau Programme</system:String>
    <system:String x:Key="EditProgram">Modifier le Programme</system:String>
    <system:String x:Key="DeactivateProgram">Désactiver le Programme</system:String>
    <system:String x:Key="RemoveProgram">Supprimer le Programme</system:String>

    <!-- Tier Form Fields -->
    <system:String x:Key="TierNameHint">Nom du Niveau</system:String>
    <system:String x:Key="MinimumPointsRequiredHint">Points Minimum Requis</system:String>
    <system:String x:Key="PointsMultiplierHint">Multiplicateur de Points</system:String>
    <system:String x:Key="BenefitsDescriptionHint">Description des Avantages</system:String>

    <!-- Tier Buttons -->
    <system:String x:Key="SaveTier">Enregistrer le Niveau</system:String>
    <system:String x:Key="AddNewTier">Ajouter un Nouveau Niveau</system:String>

    <!-- Payment Configuration -->
    <system:String x:Key="PaymentConfiguration">Configuration de Paiement</system:String>
    <system:String x:Key="ReadyToProcessPaymentFor">Prêt à traiter le paiement pour</system:String>
    <system:String x:Key="EnablePartialPaymentDescription">Activer pour saisir un montant de paiement personnalisé inférieur au total</system:String>

    <!-- Enhanced Printing -->
    <system:String x:Key="Summary">Résumé</system:String>
    <system:String x:Key="TotalInvoices">Total des Factures</system:String>
    <system:String x:Key="ReportDate">Date du Rapport</system:String>
    <system:String x:Key="GeneratedOn">Généré le</system:String>
    <system:String x:Key="UnpaidInvoices">Factures Impayées</system:String>

    <!-- Batch Stock Management -->
    <system:String x:Key="ManageBatchStock">Gérer le Stock par Lots</system:String>
    <system:String x:Key="BatchStockManagement">Gestion du Stock par Lots</system:String>
    <system:String x:Key="AddNewBatch">Ajouter un Nouveau Lot</system:String>
    <system:String x:Key="EditBatch">Modifier le Lot</system:String>
    <system:String x:Key="AddStock">Ajouter du Stock</system:String>
    <system:String x:Key="BatchNumber">Numéro de Lot</system:String>
    <system:String x:Key="ManufacturingDate">Date de Fabrication</system:String>
    <system:String x:Key="AverageCost">Coût Moyen</system:String>
    <system:String x:Key="ExpiringSoon">Expire Bientôt</system:String>
    <system:String x:Key="TotalStock">Stock Total</system:String>
    <system:String x:Key="Notes">Notes</system:String>
    <system:String x:Key="Created">Créé</system:String>
    <system:String x:Key="BatchStatistics">Statistiques des Lots</system:String>
    <system:String x:Key="BatchList">Liste des Lots</system:String>
    <system:String x:Key="BatchDetails">Détails du Lot</system:String>

    <!-- Sales History View -->
    <system:String x:Key="ShowingItemsOf">Affichage de {0} sur {1} éléments</system:String>
    <system:String x:Key="ReprintReceiptFor">Réimprimer le reçu pour la facture #{0}</system:String>
    <system:String x:Key="ReprintReceiptDialog">Réimprimer le Reçu</system:String>
    <system:String x:Key="ReprintReceiptMessage">Réimprimer le reçu pour la facture #{0}?</system:String>
    <system:String x:Key="ClickYesPrintDirectly">Cliquez Oui pour imprimer directement, Non pour afficher la boîte de dialogue d'impression, ou Annuler pour abandonner.</system:String>
    <system:String x:Key="ReprintReceiptForInvoice">Réimprimer le reçu pour la facture #{0}</system:String>
    <system:String x:Key="ReprintReceiptFullMessage">Réimprimer le reçu pour la facture #{0}?

Cliquez Oui pour imprimer directement, Non pour afficher la boîte de dialogue d'impression, ou Annuler pour abandonner.</system:String>
    <system:String x:Key="PrintingStatus">Impression...</system:String>
    <system:String x:Key="ReceiptPrintingFailed">L'impression du reçu a échoué ou a été annulée.</system:String>
    <system:String x:Key="PrintResult">Résultat de l'Impression</system:String>

    <!-- Out of Stock Dialog Messages -->
    <system:String x:Key="OutOfStockCreateInvoiceTitle">Rupture de Stock - Créer une Facture?</system:String>
    <system:String x:Key="OutOfStockCreateInvoiceMessage">Ce produit est en rupture de stock (0 articles disponibles).

Souhaitez-vous créer une facture pour ce produit à la place?</system:String>
    <system:String x:Key="OutOfStockReservationMessage">Ce produit n'a pas suffisamment de stock pour votre demande. Souhaitez-vous créer une facture de réservation pour ce produit à la place?</system:String>
    <system:String x:Key="OutOfStockTitle">Rupture de Stock</system:String>
    <system:String x:Key="OutOfStockMessage">Ce produit est en rupture de stock!</system:String>
    <system:String x:Key="OutOfStockWithQuantityMessage">Ce produit est en rupture de stock (0 {0} disponibles).</system:String>
    <system:String x:Key="StockLimitMessage">Impossible d'ajouter cette quantité. Seulement {0} {1} disponibles en stock.</system:String>
    <system:String x:Key="StockLimitTitle">Limite de Stock</system:String>

    <!-- Insufficient Stock Dialog Messages -->
    <system:String x:Key="InsufficientStockTitle">Stock Insuffisant</system:String>
    <system:String x:Key="InsufficientStockWithInvoiceMessage">Seulement {0} {1} disponibles en stock.

Ajouter le stock disponible au panier?</system:String>

    <!-- Localized Button Text -->
    <system:String x:Key="ButtonYes">Oui</system:String>
    <system:String x:Key="ButtonNo">Non</system:String>
    <system:String x:Key="ButtonCancel">Annuler</system:String>
    <system:String x:Key="ButtonOK">OK</system:String>

    <!-- Additional Dialog Messages -->
    <system:String x:Key="DialogMultipleHostsError">Plusieurs hôtes de dialogue détectés. Veuillez signaler ce problème à l'équipe de développement.</system:String>
    <system:String x:Key="DialogCashDrawerError">Erreur lors de la vérification du statut du tiroir-caisse : {0}</system:String>
    <system:String x:Key="DialogUserSwitchError">Erreur lors du changement d'utilisateur : {0}</system:String>

</ResourceDictionary>
